<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asset Test - Medical Device Management System</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="assets/vendor/bootstrap/css/bootstrap.min.css">
    
    <!-- FontAwesome CSS -->
    <link rel="stylesheet" href="assets/vendor/fontawesome/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/dark-mode.css">
    
    <style>
        .test-result {
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .test-pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">Asset Loading Test</h1>
                
                <!-- Bootstrap Test -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h3><i class="fas fa-check-circle"></i> Bootstrap Test</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="alert alert-primary" role="alert">
                                    <i class="fas fa-info-circle"></i> Primary Alert
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="alert alert-success" role="alert">
                                    <i class="fas fa-check"></i> Success Alert
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="alert alert-warning" role="alert">
                                    <i class="fas fa-exclamation-triangle"></i> Warning Alert
                                </div>
                            </div>
                        </div>
                        
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-primary">
                                <i class="fas fa-home"></i> Primary
                            </button>
                            <button type="button" class="btn btn-success">
                                <i class="fas fa-check"></i> Success
                            </button>
                            <button type="button" class="btn btn-warning">
                                <i class="fas fa-exclamation"></i> Warning
                            </button>
                            <button type="button" class="btn btn-danger">
                                <i class="fas fa-times"></i> Danger
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- FontAwesome Test -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h3><i class="fas fa-icons"></i> FontAwesome Test</h3>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-2">
                                <i class="fas fa-hospital fa-3x text-primary"></i>
                                <p>Hospital</p>
                            </div>
                            <div class="col-md-2">
                                <i class="fas fa-microscope fa-3x text-success"></i>
                                <p>Microscope</p>
                            </div>
                            <div class="col-md-2">
                                <i class="fas fa-stethoscope fa-3x text-info"></i>
                                <p>Stethoscope</p>
                            </div>
                            <div class="col-md-2">
                                <i class="fas fa-user-md fa-3x text-warning"></i>
                                <p>Doctor</p>
                            </div>
                            <div class="col-md-2">
                                <i class="fas fa-pills fa-3x text-danger"></i>
                                <p>Pills</p>
                            </div>
                            <div class="col-md-2">
                                <i class="fas fa-heartbeat fa-3x text-dark"></i>
                                <p>Heartbeat</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Custom CSS Test -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h3><i class="fas fa-palette"></i> Custom CSS Test</h3>
                    </div>
                    <div class="card-body">
                        <h2 class="text-gradient">Gradient Text</h2>
                        <div class="stats-card primary mb-3">
                            <div class="card-body">
                                <div class="stats-number">150</div>
                                <div class="stats-text">Total Devices</div>
                                <i class="fas fa-microscope stats-icon"></i>
                            </div>
                        </div>
                        
                        <div class="fade-in-up">
                            <p>This text should have a fade-in animation.</p>
                        </div>
                    </div>
                </div>
                
                <!-- Test Results -->
                <div id="test-results"></div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="assets/vendor/jquery/jquery-3.6.0.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const results = document.getElementById('test-results');
            let testResults = [];
            
            // Test Bootstrap
            const bootstrapTest = testBootstrap();
            testResults.push({
                name: 'Bootstrap',
                passed: bootstrapTest,
                message: bootstrapTest ? 'Bootstrap is loaded and working correctly' : 'Bootstrap failed to load'
            });
            
            // Test FontAwesome
            const fontAwesomeTest = testFontAwesome();
            testResults.push({
                name: 'FontAwesome',
                passed: fontAwesomeTest,
                message: fontAwesomeTest ? 'FontAwesome is loaded and working correctly' : 'FontAwesome failed to load'
            });
            
            // Test Custom CSS
            const customCSSTest = testCustomCSS();
            testResults.push({
                name: 'Custom CSS',
                passed: customCSSTest,
                message: customCSSTest ? 'Custom CSS is loaded and working correctly' : 'Custom CSS failed to load'
            });
            
            // Display results
            displayResults(testResults);
        });
        
        function testBootstrap() {
            // Check if Bootstrap classes are applied
            const testElement = document.createElement('div');
            testElement.className = 'container';
            document.body.appendChild(testElement);
            
            const computedStyle = window.getComputedStyle(testElement);
            const hasBootstrap = computedStyle.paddingLeft !== '0px' && computedStyle.paddingRight !== '0px';
            
            document.body.removeChild(testElement);
            return hasBootstrap;
        }
        
        function testFontAwesome() {
            // Check if FontAwesome is loaded by testing font-family
            const testElement = document.createElement('i');
            testElement.className = 'fas fa-home';
            document.body.appendChild(testElement);
            
            const computedStyle = window.getComputedStyle(testElement);
            const fontFamily = computedStyle.fontFamily;
            const hasFontAwesome = fontFamily.includes('Font Awesome') || fontFamily.includes('FontAwesome');
            
            document.body.removeChild(testElement);
            return hasFontAwesome;
        }
        
        function testCustomCSS() {
            // Check if custom CSS is loaded by testing a custom class
            const testElement = document.createElement('div');
            testElement.className = 'text-gradient';
            document.body.appendChild(testElement);
            
            const computedStyle = window.getComputedStyle(testElement);
            const hasGradient = computedStyle.background.includes('linear-gradient') || 
                               computedStyle.backgroundImage.includes('linear-gradient');
            
            document.body.removeChild(testElement);
            return hasGradient;
        }
        
        function displayResults(results) {
            const container = document.getElementById('test-results');
            
            results.forEach(result => {
                const resultDiv = document.createElement('div');
                resultDiv.className = `test-result ${result.passed ? 'test-pass' : 'test-fail'}`;
                resultDiv.innerHTML = `
                    <i class="fas fa-${result.passed ? 'check-circle' : 'times-circle'}"></i>
                    <strong>${result.name}:</strong> ${result.message}
                `;
                container.appendChild(resultDiv);
            });
            
            // Overall result
            const allPassed = results.every(r => r.passed);
            const overallDiv = document.createElement('div');
            overallDiv.className = `test-result ${allPassed ? 'test-pass' : 'test-fail'}`;
            overallDiv.innerHTML = `
                <h4>
                    <i class="fas fa-${allPassed ? 'check-circle' : 'times-circle'}"></i>
                    Overall Result: ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}
                </h4>
            `;
            container.appendChild(overallDiv);
        }
    </script>
</body>
</html>
