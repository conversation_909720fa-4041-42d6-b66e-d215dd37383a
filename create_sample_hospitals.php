<?php
/**
 * Create Sample Hospitals
 * This script creates sample hospital data for testing
 */

// Include the configuration and functions
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Check if user is logged in and has admin permissions
if (!isLoggedIn()) {
    die('Please log in first');
}

if (!hasPermission('manage_hospitals')) {
    die('You need admin permissions to create sample data');
}

// Get models
require_once 'models/Hospital.php';
require_once 'models/Department.php';
require_once 'models/Device.php';

$hospitalModel = new Hospital();
$departmentModel = new Department();
$deviceModel = new Device();

echo "<h1>Creating Sample Hospital Data</h1>";

// Sample hospitals data
$sampleHospitals = [
    [
        'name' => 'General Hospital',
        'address' => '123 Main Street, Downtown',
        'city' => 'New York',
        'country' => 'USA',
        'phone' => '******-0123',
        'email' => '<EMAIL>'
    ],
    [
        'name' => 'City Medical Center',
        'address' => '456 Health Avenue, Medical District',
        'city' => 'Los Angeles',
        'country' => 'USA',
        'phone' => '******-0456',
        'email' => '<EMAIL>'
    ],
    [
        'name' => 'University Hospital',
        'address' => '789 University Drive, Campus',
        'city' => 'Chicago',
        'country' => 'USA',
        'phone' => '******-0789',
        'email' => '<EMAIL>'
    ],
    [
        'name' => 'Regional Medical Center',
        'address' => '321 Care Boulevard, Suburbs',
        'city' => 'Houston',
        'country' => 'USA',
        'phone' => '******-0321',
        'email' => '<EMAIL>'
    ],
    [
        'name' => 'Children\'s Hospital',
        'address' => '654 Kids Lane, Family District',
        'city' => 'Phoenix',
        'country' => 'USA',
        'phone' => '******-0654',
        'email' => '<EMAIL>'
    ]
];

try {
    // Check if hospitals already exist
    $existingHospitals = $hospitalModel->getAll();
    
    if (count($existingHospitals) > 0) {
        echo "<p style='color: orange;'>Hospitals already exist in the database (" . count($existingHospitals) . " found)</p>";
        echo "<p>Existing hospitals:</p>";
        echo "<ul>";
        foreach ($existingHospitals as $hospital) {
            echo "<li>" . htmlspecialchars($hospital['name']) . " - " . htmlspecialchars($hospital['city']) . "</li>";
        }
        echo "</ul>";
        echo "<p><a href='" . getBaseUrl() . "/hospitals'>View Hospitals Page</a></p>";
    } else {
        echo "<p>Creating sample hospitals...</p>";
        
        foreach ($sampleHospitals as $hospitalData) {
            try {
                $hospitalId = $hospitalModel->create($hospitalData);
                if ($hospitalId) {
                    echo "<p style='color: green;'>✓ Created hospital: " . htmlspecialchars($hospitalData['name']) . " (ID: $hospitalId)</p>";
                    
                    // Create sample departments for this hospital
                    $sampleDepartments = [
                        'Emergency Department',
                        'Cardiology',
                        'Radiology',
                        'Laboratory',
                        'Surgery'
                    ];
                    
                    foreach ($sampleDepartments as $deptName) {
                        try {
                            $deptData = [
                                'name' => $deptName,
                                'hospital_id' => $hospitalId,
                                'description' => 'Sample department: ' . $deptName
                            ];
                            
                            $deptId = $departmentModel->create($deptData);
                            if ($deptId) {
                                echo "<p style='color: blue; margin-left: 20px;'>  ✓ Created department: $deptName (ID: $deptId)</p>";
                            }
                        } catch (Exception $e) {
                            echo "<p style='color: red; margin-left: 20px;'>  ✗ Failed to create department $deptName: " . $e->getMessage() . "</p>";
                        }
                    }
                    
                } else {
                    echo "<p style='color: red;'>✗ Failed to create hospital: " . htmlspecialchars($hospitalData['name']) . "</p>";
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>✗ Error creating hospital " . htmlspecialchars($hospitalData['name']) . ": " . $e->getMessage() . "</p>";
            }
        }
        
        echo "<h2>Sample Data Creation Complete!</h2>";
        echo "<p><a href='" . getBaseUrl() . "/hospitals'>View Hospitals Page</a></p>";
        echo "<p><a href='" . getBaseUrl() . "/debug_hospitals.php'>Debug Hospitals</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>";
    print_r($e->getTrace());
    echo "</pre>";
}

echo "<h2>Database Tables Check:</h2>";
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    
    // Check hospitals table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM hospitals");
    $hospitalCount = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>Hospitals in database: " . $hospitalCount['count'] . "</p>";
    
    // Check departments table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM departments");
    $deptCount = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>Departments in database: " . $deptCount['count'] . "</p>";
    
    // Check devices table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM devices");
    $deviceCount = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>Devices in database: " . $deviceCount['count'] . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>
