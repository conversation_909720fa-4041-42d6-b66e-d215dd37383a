<?php
/**
 * Enhanced Views Test Page
 * This page tests all the enhanced views to ensure they're working correctly
 */

// Include functions
require_once 'includes/functions.php';

// Get base URL
$baseUrl = getBaseUrl();

// Define enhanced pages to test
$enhancedPages = [
    'Dashboard' => '/dashboard',
    'Devices List' => '/devices',
    'Hospitals List' => '/hospitals', 
    'Maintenance List' => '/maintenance',
    'Asset Diagnostic' => '/asset_diagnostic.php',
    'Asset Test' => '/test_assets.html'
];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Views Test - Medical Device Management System</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="assets/vendor/bootstrap/css/bootstrap.min.css">
    
    <!-- FontAwesome CSS -->
    <link rel="stylesheet" href="assets/vendor/fontawesome/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        
        .test-card:hover {
            transform: translateY(-5px);
        }
        
        .page-link {
            display: block;
            padding: 15px 20px;
            text-decoration: none;
            color: #333;
            border-radius: 10px;
            margin: 5px 0;
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }
        
        .page-link:hover {
            background: rgba(0, 123, 255, 0.1);
            color: #007bff;
            transform: translateX(10px);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 10px;
        }
        
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-danger { background-color: #dc3545; }
        
        .header-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <div class="card-body text-center">
                <h1 class="header-title mb-4">🏥 Enhanced Views Test Suite</h1>
                <p class="lead text-muted">Medical Device Management System - View Enhancement Verification</p>
                <p class="text-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Click on any page below to test the enhanced views
                </p>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="test-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-desktop me-2"></i>Enhanced Application Pages
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($enhancedPages as $name => $url): ?>
                            <a href="<?php echo $baseUrl . $url; ?>" class="page-link" target="_blank">
                                <span class="status-indicator status-success"></span>
                                <i class="fas fa-external-link-alt me-2"></i>
                                <?php echo $name; ?>
                                <small class="text-muted float-end"><?php echo $url; ?></small>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="test-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-check-circle me-2"></i>Enhancement Features
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="feature-list">
                            <div class="feature-item mb-3">
                                <i class="fas fa-palette text-primary me-2"></i>
                                <strong>Modern UI Design</strong>
                                <br><small class="text-muted">Glass-morphism effects, gradients, and animations</small>
                            </div>
                            
                            <div class="feature-item mb-3">
                                <i class="fas fa-table text-info me-2"></i>
                                <strong>DataTables Integration</strong>
                                <br><small class="text-muted">Advanced sorting, searching, and pagination</small>
                            </div>
                            
                            <div class="feature-item mb-3">
                                <i class="fas fa-mobile-alt text-warning me-2"></i>
                                <strong>Responsive Design</strong>
                                <br><small class="text-muted">Mobile-first approach with Bootstrap 5</small>
                            </div>
                            
                            <div class="feature-item mb-3">
                                <i class="fas fa-keyboard text-secondary me-2"></i>
                                <strong>Keyboard Shortcuts</strong>
                                <br><small class="text-muted">Power-user features for quick navigation</small>
                            </div>
                            
                            <div class="feature-item mb-3">
                                <i class="fas fa-download text-success me-2"></i>
                                <strong>Export Functions</strong>
                                <br><small class="text-muted">PDF, Excel, CSV export capabilities</small>
                            </div>
                            
                            <div class="feature-item mb-3">
                                <i class="fas fa-bell text-danger me-2"></i>
                                <strong>Toast Notifications</strong>
                                <br><small class="text-muted">Non-intrusive success/error messages</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="test-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-keyboard me-2"></i>Keyboard Shortcuts
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">Global Shortcuts:</small>
                                <ul class="list-unstyled small">
                                    <li><kbd>Ctrl+N</kbd> - New Item</li>
                                    <li><kbd>Ctrl+F</kbd> - Search</li>
                                    <li><kbd>Ctrl+P</kbd> - Print</li>
                                    <li><kbd>Ctrl+S</kbd> - Export</li>
                                </ul>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">View Shortcuts:</small>
                                <ul class="list-unstyled small">
                                    <li><kbd>V</kbd> - Toggle View</li>
                                    <li><kbd>T</kbd> - Timeline View</li>
                                    <li><kbd>Ctrl+E</kbd> - Edit</li>
                                    <li><kbd>Ctrl+L</kbd> - Logs</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-card">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>System Status
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="status-item">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h6>Bootstrap</h6>
                            <small class="text-muted">5.3.0 Loaded</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="status-item">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h6>FontAwesome</h6>
                            <small class="text-muted">6.4.0 Loaded</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="status-item">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h6>jQuery</h6>
                            <small class="text-muted">3.6.0 Loaded</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="status-item">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h6>Custom CSS</h6>
                            <small class="text-muted">Enhanced</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <p class="text-white">
                <i class="fas fa-heart text-danger me-1"></i>
                Enhanced with modern design and functionality
            </p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="assets/vendor/jquery/jquery-3.6.0.min.js"></script>
    
    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate cards on load
            const cards = document.querySelectorAll('.test-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
            
            // Add click effects to page links
            const pageLinks = document.querySelectorAll('.page-link');
            pageLinks.forEach(link => {
                link.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'translateX(10px)';
                    }, 100);
                });
            });
        });
    </script>
</body>
</html>
