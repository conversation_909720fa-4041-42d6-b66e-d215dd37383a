<?php
// Simple test to check CSS loading without complex routing
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct CSS Test</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS with direct paths -->
    <link rel="stylesheet" href="./assets/css/style.css">
    <link rel="stylesheet" href="./assets/css/dark-mode.css">
    
    <style>
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-primary">Direct CSS Loading Test</h1>
        
        <div class="debug-info">
            <h5>Debug Information:</h5>
            <p><strong>Current URL:</strong> <?php echo $_SERVER['REQUEST_URI']; ?></p>
            <p><strong>Script Name:</strong> <?php echo $_SERVER['SCRIPT_NAME']; ?></p>
            <p><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT']; ?></p>
            <p><strong>Current Directory:</strong> <?php echo __DIR__; ?></p>
        </div>
        
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            If you can see this green alert with an icon, Bootstrap and FontAwesome are loading correctly.
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-test me-2"></i>Test Card
                </h5>
            </div>
            <div class="card-body">
                <p>This is a test card to verify Bootstrap styling is working.</p>
                <button class="btn btn-primary">
                    <i class="fas fa-check me-2"></i>Test Button
                </button>
                <button class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-cog me-2"></i>Secondary Button
                </button>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>Custom CSS Test</h3>
            <div class="card-glass p-3 mb-3" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 1rem;">
                <p>This should have a glass effect if custom CSS is loading. If not, it has inline fallback styling.</p>
            </div>
            
            <div class="stats-card primary p-3" style="border: 1px solid #007bff; border-radius: 0.5rem;">
                <p>This should have enhanced styling if custom CSS is loading. If not, it has inline fallback styling.</p>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>CSS File Accessibility Test</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>Style.css</h5>
                    <a href="./assets/css/style.css" target="_blank" class="btn btn-outline-primary">
                        <i class="fas fa-external-link-alt me-2"></i>Open style.css
                    </a>
                </div>
                <div class="col-md-6">
                    <h5>Dark-mode.css</h5>
                    <a href="./assets/css/dark-mode.css" target="_blank" class="btn btn-outline-primary">
                        <i class="fas fa-external-link-alt me-2"></i>Open dark-mode.css
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Test if CSS files are loading
        document.addEventListener('DOMContentLoaded', function() {
            // Check if Bootstrap is loaded
            const alertElement = document.querySelector('.alert-success');
            const computedStyle = window.getComputedStyle(alertElement);
            const backgroundColor = computedStyle.backgroundColor;
            
            console.log('Alert background color:', backgroundColor);
            
            // Check if custom CSS is loaded
            const glassElement = document.querySelector('.card-glass');
            if (glassElement) {
                const glassStyle = window.getComputedStyle(glassElement);
                console.log('Glass element backdrop-filter:', glassStyle.backdropFilter);
            }
        });
    </script>
</body>
</html>
