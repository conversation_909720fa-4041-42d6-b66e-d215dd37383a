<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple CSS Test - Medical Device Management</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="./assets/css/style.css">
    <link rel="stylesheet" href="./assets/css/dark-mode.css">
    
    <style>
        .test-status {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            border: 2px solid;
        }
        .test-pass {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .test-fail {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Test Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-dark sidebar">
                <div class="position-sticky pt-3 sidebar-sticky">
                    <div class="text-center mb-4">
                        <h5 class="text-white">CSS Test</h5>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#">
                                <i class="fas fa-test-tube me-2"></i>CSS Test
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-hospital me-2"></i>Hospitals
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-microscope me-2"></i>Devices
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 text-gradient mb-1">CSS Loading Test</h1>
                        <p class="text-muted mb-0">Testing if custom CSS styles are loading correctly</p>
                    </div>
                </div>
                
                <!-- Test Results -->
                <div id="test-results">
                    <h3>Test Results:</h3>
                    <div id="bootstrap-test" class="test-status test-fail">
                        <strong>Bootstrap Test:</strong> <span id="bootstrap-result">Testing...</span>
                    </div>
                    <div id="fontawesome-test" class="test-status test-fail">
                        <strong>FontAwesome Test:</strong> <span id="fontawesome-result">Testing...</span>
                    </div>
                    <div id="custom-css-test" class="test-status test-fail">
                        <strong>Custom CSS Test:</strong> <span id="custom-css-result">Testing...</span>
                    </div>
                </div>
                
                <!-- Bootstrap Components Test -->
                <div class="alert alert-success fade-in-up">
                    <i class="fas fa-check-circle me-2"></i>
                    This is a Bootstrap alert with FontAwesome icon
                </div>
                
                <!-- Custom CSS Components Test -->
                <div class="card card-glass fade-in-up mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-vial me-2"></i>Glass Card Test
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>This card should have a glass effect if custom CSS is loading.</p>
                    </div>
                </div>
                
                <!-- Stats Cards Test -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="card stats-card primary h-100 fade-in-up">
                            <div class="card-body text-center">
                                <div class="stats-icon mb-3">
                                    <i class="fas fa-hospital fa-2x text-primary"></i>
                                </div>
                                <div class="stats-number">42</div>
                                <div class="stats-text">Test Number</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card success h-100 fade-in-up" style="animation-delay: 0.1s;">
                            <div class="card-body text-center">
                                <div class="stats-icon mb-3">
                                    <i class="fas fa-check-circle fa-2x text-success"></i>
                                </div>
                                <div class="stats-number">100</div>
                                <div class="stats-text">Success Rate</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Table Test -->
                <div class="card fade-in-up mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-table me-2"></i>Table Test
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Component</th>
                                        <th>Status</th>
                                        <th>Notes</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Bootstrap CSS</td>
                                        <td><span class="badge bg-success">Loaded</span></td>
                                        <td>External CDN</td>
                                    </tr>
                                    <tr>
                                        <td>FontAwesome</td>
                                        <td><span class="badge bg-success">Loaded</span></td>
                                        <td>External CDN</td>
                                    </tr>
                                    <tr>
                                        <td>Custom CSS</td>
                                        <td><span class="badge bg-warning">Testing</span></td>
                                        <td>Local files</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Direct CSS Links Test -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Direct CSS File Access</h5>
                    </div>
                    <div class="card-body">
                        <p>Click these links to test direct access to CSS files:</p>
                        <div class="d-flex gap-2">
                            <a href="./assets/css/style.css" target="_blank" class="btn btn-outline-primary">
                                <i class="fas fa-external-link-alt me-2"></i>style.css
                            </a>
                            <a href="./assets/css/dark-mode.css" target="_blank" class="btn btn-outline-primary">
                                <i class="fas fa-external-link-alt me-2"></i>dark-mode.css
                            </a>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Starting CSS tests...');
            
            // Test Bootstrap
            const alertElement = document.querySelector('.alert-success');
            const alertStyles = window.getComputedStyle(alertElement);
            const alertBg = alertStyles.backgroundColor;
            
            if (alertBg.includes('212, 237, 218') || alertBg.includes('rgb(212, 237, 218)')) {
                document.getElementById('bootstrap-test').className = 'test-status test-pass';
                document.getElementById('bootstrap-result').textContent = 'PASS - Bootstrap is loading correctly';
            } else {
                document.getElementById('bootstrap-result').textContent = 'FAIL - Bootstrap not loading (bg: ' + alertBg + ')';
            }
            
            // Test FontAwesome
            const iconElement = document.querySelector('.fas.fa-check-circle');
            const iconStyles = window.getComputedStyle(iconElement, ':before');
            const fontFamily = iconStyles.fontFamily;
            
            if (fontFamily.includes('Font Awesome') || fontFamily.includes('FontAwesome')) {
                document.getElementById('fontawesome-test').className = 'test-status test-pass';
                document.getElementById('fontawesome-result').textContent = 'PASS - FontAwesome is loading correctly';
            } else {
                document.getElementById('fontawesome-result').textContent = 'FAIL - FontAwesome not loading (font: ' + fontFamily + ')';
            }
            
            // Test Custom CSS
            const glassCard = document.querySelector('.card-glass');
            const glassStyles = window.getComputedStyle(glassCard);
            const backdropFilter = glassStyles.backdropFilter || glassStyles.webkitBackdropFilter;
            
            if (backdropFilter && backdropFilter !== 'none') {
                document.getElementById('custom-css-test').className = 'test-status test-pass';
                document.getElementById('custom-css-result').textContent = 'PASS - Custom CSS is loading correctly';
            } else {
                document.getElementById('custom-css-result').textContent = 'FAIL - Custom CSS not loading (backdrop-filter: ' + backdropFilter + ')';
            }
            
            // Test animations
            const fadeElements = document.querySelectorAll('.fade-in-up');
            console.log('Found ' + fadeElements.length + ' elements with fade-in-up animation');
            
            // Log all CSS file requests
            const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
            console.log('CSS files being loaded:');
            cssLinks.forEach(link => {
                console.log('- ' + link.href);
            });
        });
    </script>
</body>
</html>
