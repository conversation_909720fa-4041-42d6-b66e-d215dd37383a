<?php
/**
 * Test Login Fix
 * 
 * This script tests if the login issues have been resolved.
 */

// Start session
session_start();

// Load database configuration
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

echo "<h1>Login Fix Test</h1>";

try {
    // Test 1: Check if permissions column exists
    echo "<h3>Test 1: Check permissions column</h3>";
    $stmt = $pdo->prepare("SHOW COLUMNS FROM users LIKE 'permissions'");
    $stmt->execute();
    $permissionsColumn = $stmt->fetch();
    
    if ($permissionsColumn) {
        echo "<p style='color: green;'>✓ Permissions column exists</p>";
    } else {
        echo "<p style='color: red;'>✗ Permissions column missing</p>";
    }
    
    // Test 2: Check if activity_logs table exists
    echo "<h3>Test 2: Check activity_logs table</h3>";
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'activity_logs'");
    $stmt->execute();
    $activityLogsTable = $stmt->fetch();
    
    if ($activityLogsTable) {
        echo "<p style='color: green;'>✓ Activity logs table exists</p>";
    } else {
        echo "<p style='color: red;'>✗ Activity logs table missing</p>";
    }
    
    // Test 3: Check admin user permissions
    echo "<h3>Test 3: Check admin user permissions</h3>";
    $stmt = $pdo->prepare("SELECT username, role, permissions FROM users WHERE username = 'admin'");
    $stmt->execute();
    $adminUser = $stmt->fetch();
    
    if ($adminUser) {
        echo "<p style='color: green;'>✓ Admin user found</p>";
        echo "<p>Role: " . htmlspecialchars($adminUser['role']) . "</p>";
        
        if (!empty($adminUser['permissions'])) {
            $permissions = json_decode($adminUser['permissions'], true);
            if (is_array($permissions)) {
                echo "<p style='color: green;'>✓ Permissions are properly formatted JSON</p>";
                echo "<p>Permissions: " . implode(', ', $permissions) . "</p>";
            } else {
                echo "<p style='color: red;'>✗ Permissions are not valid JSON</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠ Admin user has no permissions set</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Admin user not found</p>";
    }
    
    // Test 4: Test authentication function
    echo "<h3>Test 4: Test authentication function</h3>";
    if (function_exists('authenticate')) {
        echo "<p style='color: green;'>✓ Authenticate function exists</p>";
    } else {
        echo "<p style='color: red;'>✗ Authenticate function missing</p>";
    }
    
    // Test 5: Test activity logging
    echo "<h3>Test 5: Test activity logging</h3>";
    try {
        logAction('test', 'Testing activity logging');
        echo "<p style='color: green;'>✓ Activity logging works</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Activity logging failed: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3>Summary</h3>";
    if ($permissionsColumn && $activityLogsTable && $adminUser) {
        echo "<p style='color: green; font-weight: bold;'>✓ All tests passed! Login should work now.</p>";
        echo '<p><a href="login.php" style="display: inline-block; padding: 10px 15px; background-color: #007bff; color: #fff; text-decoration: none; border-radius: 3px;">Try Login</a></p>';
    } else {
        echo "<p style='color: red; font-weight: bold;'>✗ Some tests failed. Please run the migration script first.</p>";
        echo '<p><a href="fix_login.php" style="display: inline-block; padding: 10px 15px; background-color: #dc3545; color: #fff; text-decoration: none; border-radius: 3px;">Run Migration</a></p>';
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database Error: " . $e->getMessage() . "</p>";
}
?>
