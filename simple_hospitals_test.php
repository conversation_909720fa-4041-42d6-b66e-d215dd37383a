<?php
/**
 * Simple Hospitals Test
 * This page tests the basic hospitals functionality without complex styling
 */

// Include the configuration and functions
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('login');
}

// Get models
require_once 'models/Hospital.php';
require_once 'models/Department.php';
require_once 'models/Device.php';

$hospitalModel = new Hospital($pdo);
$departmentModel = new Department($pdo);
$deviceModel = new Device($pdo);

// Get current user
$currentUser = getCurrentUser();
$userHospitalId = $currentUser['hospital_id'];

// Get hospitals
if ($userHospitalId && !hasPermission('manage_hospitals')) {
    $hospitals = [$hospitalModel->getById($userHospitalId)];
} else {
    $hospitals = $hospitalModel->getAll();
}

if (!is_array($hospitals)) {
    $hospitals = [];
}

// Add additional data for each hospital
foreach ($hospitals as &$hospital) {
    $hospital['department_count'] = $departmentModel->countByHospital($hospital['id']);
    $hospital['device_count'] = $deviceModel->countByHospital($hospital['id']);
    
    if (!isset($hospital['city'])) $hospital['city'] = '';
    if (!isset($hospital['country'])) $hospital['country'] = '';
    if (!isset($hospital['email'])) $hospital['email'] = '';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Hospitals Test - Medical Device Management System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- FontAwesome CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
        }
        
        .container {
            padding: 20px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .header-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
        }
        
        .hospital-card {
            transition: transform 0.2s ease;
        }
        
        .hospital-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-body text-center">
                <h1 class="header-title mb-4">🏥 Simple Hospitals Test</h1>
                <p class="lead text-muted">Medical Device Management System - Basic Hospitals View</p>
                <p class="text-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Found <?php echo count($hospitals); ?> hospital(s)
                </p>
            </div>
        </div>

        <?php if (empty($hospitals)): ?>
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-hospital fa-3x text-muted mb-3"></i>
                    <h5>No Hospitals Found</h5>
                    <p class="text-muted">No hospitals are available in the system.</p>
                    <?php if (hasPermission('manage_hospitals')): ?>
                        <a href="create_sample_hospitals.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Create Sample Hospitals
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        <?php else: ?>
            <div class="row">
                <?php foreach ($hospitals as $hospital): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card hospital-card h-100">
                            <div class="card-header bg-primary text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-hospital me-2"></i>
                                    <?php echo htmlspecialchars($hospital['name']); ?>
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <small class="text-muted">Address:</small>
                                    <div><?php echo htmlspecialchars($hospital['address']); ?></div>
                                </div>
                                
                                <?php if (!empty($hospital['city']) || !empty($hospital['country'])): ?>
                                <div class="mb-3">
                                    <small class="text-muted">Location:</small>
                                    <div>
                                        <i class="fas fa-map-marker-alt text-danger me-1"></i>
                                        <?php echo htmlspecialchars($hospital['city']); ?>
                                        <?php if (!empty($hospital['country'])): ?>
                                            , <?php echo htmlspecialchars($hospital['country']); ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <div class="mb-3">
                                    <small class="text-muted">Contact:</small>
                                    <div>
                                        <i class="fas fa-phone text-primary me-1"></i>
                                        <?php echo htmlspecialchars($hospital['phone']); ?>
                                    </div>
                                    <?php if (!empty($hospital['email'])): ?>
                                    <div>
                                        <i class="fas fa-envelope text-info me-1"></i>
                                        <?php echo htmlspecialchars($hospital['email']); ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="border-end">
                                            <h4 class="text-info"><?php echo (int)$hospital['department_count']; ?></h4>
                                            <small class="text-muted">Departments</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <h4 class="text-success"><?php echo (int)$hospital['device_count']; ?></h4>
                                        <small class="text-muted">Devices</small>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <div class="d-flex justify-content-between">
                                    <a href="<?php echo getBaseUrl(); ?>/hospitals/view/<?php echo $hospital['id']; ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye me-1"></i>View
                                    </a>
                                    <?php if (hasPermission('manage_hospitals')): ?>
                                    <div class="btn-group">
                                        <a href="<?php echo getBaseUrl(); ?>/hospitals/edit/<?php echo $hospital['id']; ?>" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>System Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>User Information:</h6>
                        <ul class="list-unstyled">
                            <li><strong>Name:</strong> <?php echo htmlspecialchars($currentUser['full_name']); ?></li>
                            <li><strong>Role:</strong> <?php echo htmlspecialchars($currentUser['role']); ?></li>
                            <li><strong>Hospital ID:</strong> <?php echo $userHospitalId ?: 'None'; ?></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Permissions:</h6>
                        <ul class="list-unstyled">
                            <li>View Hospitals: <?php echo hasPermission('view_hospitals') ? '✓' : '✗'; ?></li>
                            <li>Manage Hospitals: <?php echo hasPermission('manage_hospitals') ? '✓' : '✗'; ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <div class="btn-group">
                <a href="<?php echo getBaseUrl(); ?>/hospitals" class="btn btn-primary">
                    <i class="fas fa-hospital me-2"></i>Go to Full Hospitals Page
                </a>
                <a href="<?php echo getBaseUrl(); ?>/dashboard" class="btn btn-outline-primary">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
                <a href="debug_hospitals.php" class="btn btn-outline-info">
                    <i class="fas fa-bug me-2"></i>Debug
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Simple hospitals test loaded successfully');
            console.log('Hospitals found: <?php echo count($hospitals); ?>');
            
            // Animate cards on load
            const cards = document.querySelectorAll('.hospital-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
