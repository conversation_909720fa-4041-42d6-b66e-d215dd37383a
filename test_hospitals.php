<?php
// Simple test page to bypass routing
session_start();

// Include necessary files
require_once 'includes/functions.php';

// Set fake user session for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_role'] = 'admin';
    $_SESSION['user_name'] = 'Test User';
    $_SESSION['hospital_id'] = 1;
}

// Create sample hospitals data for testing (no database needed)
$hospitals = [
    [
        'id' => 1,
        'name' => 'General Hospital',
        'city' => 'New York',
        'country' => 'USA',
        'phone' => '******-0123'
    ],
    [
        'id' => 2,
        'name' => 'Medical Center',
        'city' => 'Los Angeles',
        'country' => 'USA',
        'phone' => '******-0456'
    ],
    [
        'id' => 3,
        'name' => 'University Hospital',
        'city' => 'Chicago',
        'country' => 'USA',
        'phone' => '******-0789'
    ]
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Hospitals - Medical Device Management</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS with absolute paths -->
    <link rel="stylesheet" href="./assets/css/style.css">
    <link rel="stylesheet" href="./assets/css/dark-mode.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-dark sidebar">
                <div class="position-sticky pt-3 sidebar-sticky">
                    <div class="text-center mb-4">
                        <h5 class="text-white">Medical Device System</h5>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="./dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="./test_hospitals.php">
                                <i class="fas fa-hospital me-2"></i>Hospitals
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 text-gradient mb-1">Hospitals Management</h1>
                        <p class="text-muted mb-0">Manage hospital facilities and their information</p>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <a href="#" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add Hospital
                        </a>
                        
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-download me-2"></i>Export
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#">
                                    <i class="fas fa-file-pdf me-2 text-danger"></i>PDF
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="fas fa-file-excel me-2 text-success"></i>Excel
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- Test Alert -->
                <div class="alert alert-success fade-in-up">
                    <i class="fas fa-check-circle me-2"></i>
                    If you can see this styled alert with proper colors and animations, the CSS is loading correctly!
                </div>
                
                <!-- Enhanced Hospitals Table -->
                <div class="card card-glass fade-in-up" style="animation-delay: 0.2s;">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-list me-2"></i>Hospitals List
                            </h5>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>City</th>
                                        <th>Country</th>
                                        <th>Phone</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($hospitals)): ?>
                                    <tr>
                                        <td colspan="5" class="text-center py-4">
                                            <i class="fas fa-hospital fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">No hospitals found. This is just a test page.</p>
                                        </td>
                                    </tr>
                                    <?php else: ?>
                                        <?php foreach ($hospitals as $hospital): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($hospital['name']); ?></td>
                                            <td><?php echo htmlspecialchars($hospital['city']); ?></td>
                                            <td><?php echo htmlspecialchars($hospital['country']); ?></td>
                                            <td><?php echo htmlspecialchars($hospital['phone']); ?></td>
                                            <td>
                                                <div class="btn-group">
                                                    <button class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-warning">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Test Stats Cards -->
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="card stats-card primary h-100 fade-in-up">
                            <div class="card-body text-center">
                                <div class="stats-icon mb-3">
                                    <i class="fas fa-hospital fa-2x text-primary"></i>
                                </div>
                                <div class="stats-number">25</div>
                                <div class="stats-text">Total Hospitals</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card success h-100 fade-in-up" style="animation-delay: 0.1s;">
                            <div class="card-body text-center">
                                <div class="stats-icon mb-3">
                                    <i class="fas fa-check-circle fa-2x text-success"></i>
                                </div>
                                <div class="stats-number">150</div>
                                <div class="stats-text">Active Devices</div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        console.log('Test page loaded successfully');
        
        // Test if CSS animations are working
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, testing CSS...');
            
            // Check if custom CSS classes exist
            const testElement = document.querySelector('.card-glass');
            if (testElement) {
                const styles = window.getComputedStyle(testElement);
                console.log('Glass card backdrop-filter:', styles.backdropFilter);
                console.log('Glass card background:', styles.background);
            }
        });
    </script>
</body>
</html>
