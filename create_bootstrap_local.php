<?php
/**
 * Create minimal Bootstrap CSS locally
 * This creates a basic Bootstrap-like CSS file locally
 */

// Create directories
$dirs = [
    'assets/vendor',
    'assets/vendor/bootstrap',
    'assets/vendor/bootstrap/css',
    'assets/vendor/bootstrap/js',
    'assets/vendor/fontawesome',
    'assets/vendor/fontawesome/css'
];

foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "Created directory: {$dir}<br>";
    }
}

// Create minimal Bootstrap CSS
$bootstrapCSS = '
/* Minimal Bootstrap CSS for Medical Device Management System */

/* Reset and Base */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
}

/* Container */
.container-fluid {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

/* Grid System */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-md-3 { flex: 0 0 25%; max-width: 25%; }
.col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-md-6 { flex: 0 0 50%; max-width: 50%; }
.col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-md-9 { flex: 0 0 75%; max-width: 75%; }
.col-md-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-lg-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-lg-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }

[class*="col-"] {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

/* Utilities */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-md-block { display: block !important; }
.d-grid { display: grid !important; }

.justify-content-between { justify-content: space-between !important; }
.align-items-center { align-items: center !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-md-nowrap { flex-wrap: nowrap !important; }

.text-center { text-align: center !important; }
.text-white { color: #fff !important; }
.text-muted { color: #6c757d !important; }
.text-primary { color: #007bff !important; }
.text-success { color: #28a745 !important; }
.text-danger { color: #dc3545 !important; }
.text-warning { color: #ffc107 !important; }
.text-info { color: #17a2b8 !important; }

.bg-dark { background-color: #343a40 !important; }
.bg-primary { background-color: #007bff !important; }
.bg-success { background-color: #28a745 !important; }
.bg-danger { background-color: #dc3545 !important; }
.bg-warning { background-color: #ffc107 !important; }
.bg-info { background-color: #17a2b8 !important; }

/* Spacing */
.m-0 { margin: 0 !important; }
.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.me-1 { margin-right: 0.25rem !important; }
.me-2 { margin-right: 0.5rem !important; }
.ms-sm-auto { margin-left: auto !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

.p-0 { padding: 0 !important; }
.p-3 { padding: 1rem !important; }
.pt-3 { padding-top: 1rem !important; }
.pt-4 { padding-top: 1.5rem !important; }
.pb-3 { padding-bottom: 1rem !important; }
.px-md-4 { padding-left: 1.5rem !important; padding-right: 1.5rem !important; }

/* Typography */
.h2 { font-size: 2rem; font-weight: 500; line-height: 1.2; }
.h3 { font-size: 1.75rem; font-weight: 500; line-height: 1.2; }
.h5 { font-size: 1.25rem; font-weight: 500; line-height: 1.2; }

/* Buttons */
.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.375rem;
    text-decoration: none;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    color: #fff;
    background-color: #0056b3;
    border-color: #004085;
}

.btn-outline-primary {
    color: #007bff;
    border-color: #007bff;
    background-color: transparent;
}

.btn-outline-primary:hover {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
    background-color: transparent;
}

.btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
    background-color: transparent;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
}

.btn-group {
    position: relative;
    display: inline-flex;
    vertical-align: middle;
}

.btn-group > .btn {
    position: relative;
    flex: 1 1 auto;
}

.btn-toolbar {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
}

/* Dropdown */
.dropdown {
    position: relative;
}

.dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 10rem;
    padding: 0.5rem 0;
    margin: 0.125rem 0 0;
    font-size: 1rem;
    color: #212529;
    text-align: left;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 0.375rem 1.5rem;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: inherit;
    text-decoration: none;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
}

.dropdown-item:hover {
    color: #1e2125;
    background-color: #e9ecef;
}

/* Alerts */
.alert {
    position: relative;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-dismissible {
    padding-right: 4rem;
}

.btn-close {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    padding: 0.75rem 1.25rem;
    color: inherit;
    background: transparent;
    border: 0;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    opacity: 0.5;
    cursor: pointer;
}

/* Cards */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0.375rem;
}

.card-header {
    padding: 0.75rem 1.25rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    border-top-left-radius: calc(0.375rem - 1px);
    border-top-right-radius: calc(0.375rem - 1px);
}

.card-body {
    flex: 1 1 auto;
    padding: 1.25rem;
}

.card-title {
    margin-bottom: 0.75rem;
}

/* Tables */
.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
}

.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: top;
    border-bottom: 1px solid #dee2e6;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    color: #212529;
    background-color: rgba(0, 0, 0, 0.075);
}

/* Navigation */
.nav {
    display: flex;
    flex-wrap: wrap;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
}

.nav-link {
    display: block;
    padding: 0.5rem 1rem;
    color: #007bff;
    text-decoration: none;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

.nav-link:hover {
    color: #0056b3;
}

.nav-link.active {
    color: #495057;
}

.flex-column {
    flex-direction: column !important;
}

/* Forms */
.form-control {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select {
    display: block;
    width: 100%;
    padding: 0.375rem 2.25rem 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-label {
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group > .form-control {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
}

/* Badges */
.badge {
    display: inline-block;
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
}

.bg-success { background-color: #28a745 !important; }
.bg-warning { background-color: #ffc107 !important; color: #212529 !important; }
.bg-danger { background-color: #dc3545 !important; }
.bg-secondary { background-color: #6c757d !important; }

/* Position utilities */
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-sticky { position: sticky !important; }

.top-0 { top: 0 !important; }
.start-100 { left: 100% !important; }
.translate-middle { transform: translate(-50%, -50%) !important; }

/* Border radius */
.rounded-pill { border-radius: 50rem !important; }

/* Height utilities */
.h-100 { height: 100% !important; }

/* Collapse */
.collapse:not(.show) { display: none; }

/* Responsive utilities */
@media (min-width: 768px) {
    .d-md-block { display: block !important; }
    .col-md-3 { flex: 0 0 25%; max-width: 25%; }
    .col-md-9 { flex: 0 0 75%; max-width: 75%; }
}

/* Animation utilities */
.fade {
    transition: opacity 0.15s linear;
}

.fade:not(.show) {
    opacity: 0;
}

.fade.show {
    opacity: 1;
}

/* Shadow utilities */
.shadow-sm { box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important; }
.shadow-lg { box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important; }

/* Border utilities */
.border-0 { border: 0 !important; }
';

// Write Bootstrap CSS
file_put_contents('assets/vendor/bootstrap/css/bootstrap.min.css', $bootstrapCSS);
echo "✓ Created minimal Bootstrap CSS<br>";

// Create minimal FontAwesome CSS with Unicode symbols
$fontAwesomeCSS = '
/* Minimal FontAwesome CSS with Unicode symbols */
.fas, .fa {
    font-family: inherit;
    font-weight: normal;
    font-style: normal;
    text-decoration: none;
}

/* Common icons using Unicode symbols */
.fa-tachometer-alt::before, .fa-dashboard::before { content: "📊"; }
.fa-hospital::before { content: "🏥"; }
.fa-building::before { content: "🏢"; }
.fa-microscope::before { content: "🔬"; }
.fa-tools::before { content: "🔧"; }
.fa-ticket-alt::before { content: "🎫"; }
.fa-chart-bar::before { content: "📊"; }
.fa-users::before { content: "👥"; }
.fa-user::before { content: "👤"; }
.fa-sign-out-alt::before { content: "🚪"; }
.fa-bell::before { content: "🔔"; }
.fa-globe::before { content: "🌐"; }
.fa-moon::before { content: "🌙"; }
.fa-sun::before { content: "☀️"; }
.fa-plus::before { content: "+"; }
.fa-edit::before { content: "✏️"; }
.fa-trash::before { content: "🗑️"; }
.fa-eye::before { content: "👁️"; }
.fa-download::before { content: "⬇️"; }
.fa-upload::before { content: "⬆️"; }
.fa-search::before { content: "🔍"; }
.fa-filter::before { content: "🔽"; }
.fa-times::before { content: "✕"; }
.fa-check::before { content: "✓"; }
.fa-check-circle::before { content: "✅"; }
.fa-times-circle::before { content: "❌"; }
.fa-exclamation-triangle::before { content: "⚠️"; }
.fa-info-circle::before { content: "ℹ️"; }
.fa-cog::before { content: "⚙️"; }
.fa-wrench::before { content: "🔧"; }
.fa-calendar::before { content: "📅"; }
.fa-calendar-times::before { content: "📅"; }
.fa-laptop-medical::before { content: "💻"; }
.fa-file-pdf::before { content: "📄"; }
.fa-file-excel::before { content: "📊"; }
.fa-file-csv::before { content: "📋"; }
.fa-external-link-alt::before { content: "🔗"; }
.fa-list::before { content: "📋"; }
.fa-flag::before { content: "🏳️"; }
.fa-flag-usa::before { content: "🇺🇸"; }

/* Spacing for icons */
.me-1 { margin-right: 0.25rem; }
.me-2 { margin-right: 0.5rem; }
.ms-2 { margin-left: 0.5rem; }

/* Icon sizes */
.fa-2x { font-size: 2em; }
.fa-3x { font-size: 3em; }
';

// Write FontAwesome CSS
file_put_contents('assets/vendor/fontawesome/css/all.min.css', $fontAwesomeCSS);
echo "✓ Created minimal FontAwesome CSS with Unicode symbols<br>";

// Create minimal Bootstrap JS
$bootstrapJS = '
/* Minimal Bootstrap JS functionality */
(function() {
    "use strict";
    
    // Dropdown functionality
    document.addEventListener("DOMContentLoaded", function() {
        const dropdownToggles = document.querySelectorAll("[data-bs-toggle=\"dropdown\"]");
        
        dropdownToggles.forEach(function(toggle) {
            toggle.addEventListener("click", function(e) {
                e.preventDefault();
                const menu = toggle.nextElementSibling;
                if (menu && menu.classList.contains("dropdown-menu")) {
                    menu.style.display = menu.style.display === "block" ? "none" : "block";
                }
            });
        });
        
        // Close dropdowns when clicking outside
        document.addEventListener("click", function(e) {
            if (!e.target.closest(".dropdown")) {
                const openMenus = document.querySelectorAll(".dropdown-menu");
                openMenus.forEach(function(menu) {
                    menu.style.display = "none";
                });
            }
        });
        
        // Alert dismiss functionality
        const alertCloses = document.querySelectorAll("[data-bs-dismiss=\"alert\"]");
        alertCloses.forEach(function(closeBtn) {
            closeBtn.addEventListener("click", function() {
                const alert = closeBtn.closest(".alert");
                if (alert) {
                    alert.style.display = "none";
                }
            });
        });
    });
})();
';

// Write Bootstrap JS
file_put_contents('assets/vendor/bootstrap/js/bootstrap.bundle.min.js', $bootstrapJS);
echo "✓ Created minimal Bootstrap JS<br>";

echo "<h2>✅ Local assets created successfully!</h2>";
echo "<p>Now test your application - it should work with local assets.</p>";
echo "<p><a href='test_local_assets.php'>Test Local Assets</a></p>";
echo "<p><a href='index.php'>Go to Main Application</a></p>";
?>
