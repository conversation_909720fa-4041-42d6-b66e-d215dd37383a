<?php
// Debug file paths and URL generation
session_start();

// Include functions if available
$functionsLoaded = false;
try {
    require_once 'includes/functions.php';
    $functionsLoaded = true;
} catch (Exception $e) {
    $functionsError = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Paths - Medical Device Management</title>
    
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .debug-section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .error { background: #f8d7da; border-left-color: #dc3545; color: #721c24; }
        .success { background: #d4edda; border-left-color: #28a745; color: #155724; }
        .warning { background: #fff3cd; border-left-color: #ffc107; color: #856404; }
        .code { background: #e9ecef; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .test-link { display: inline-block; margin: 5px; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
        .test-link:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Path and URL Debug Information</h1>
        
        <!-- Server Information -->
        <div class="debug-section">
            <h2>Server Information</h2>
            <table>
                <tr><th>Variable</th><th>Value</th></tr>
                <tr><td>HTTP_HOST</td><td><?php echo $_SERVER['HTTP_HOST'] ?? 'Not set'; ?></td></tr>
                <tr><td>SERVER_NAME</td><td><?php echo $_SERVER['SERVER_NAME'] ?? 'Not set'; ?></td></tr>
                <tr><td>REQUEST_URI</td><td><?php echo $_SERVER['REQUEST_URI'] ?? 'Not set'; ?></td></tr>
                <tr><td>SCRIPT_NAME</td><td><?php echo $_SERVER['SCRIPT_NAME'] ?? 'Not set'; ?></td></tr>
                <tr><td>DOCUMENT_ROOT</td><td><?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Not set'; ?></td></tr>
                <tr><td>Current Directory</td><td><?php echo __DIR__; ?></td></tr>
                <tr><td>HTTPS</td><td><?php echo isset($_SERVER['HTTPS']) ? $_SERVER['HTTPS'] : 'Not set'; ?></td></tr>
            </table>
        </div>
        
        <!-- Functions Status -->
        <div class="debug-section <?php echo $functionsLoaded ? 'success' : 'error'; ?>">
            <h2>Functions.php Status</h2>
            <?php if ($functionsLoaded): ?>
                <p>✓ Functions.php loaded successfully</p>
                <p><strong>getBaseUrl():</strong> <code><?php echo getBaseUrl(); ?></code></p>
            <?php else: ?>
                <p>✗ Failed to load functions.php</p>
                <p><strong>Error:</strong> <?php echo $functionsError ?? 'Unknown error'; ?></p>
            <?php endif; ?>
        </div>
        
        <!-- File Existence Check -->
        <div class="debug-section">
            <h2>CSS File Existence Check</h2>
            <table>
                <tr><th>File</th><th>Exists</th><th>Size</th><th>Readable</th><th>Full Path</th></tr>
                <?php
                $cssFiles = [
                    'assets/css/style.css',
                    'assets/css/dark-mode.css',
                    'assets/vendor/bootstrap/css/bootstrap.min.css',
                    'assets/vendor/fontawesome/css/all.min.css'
                ];
                
                foreach ($cssFiles as $file) {
                    $fullPath = __DIR__ . '/' . $file;
                    $exists = file_exists($file);
                    $size = $exists ? filesize($file) : 0;
                    $readable = $exists ? is_readable($file) : false;
                    
                    echo "<tr>";
                    echo "<td>{$file}</td>";
                    echo "<td>" . ($exists ? '✓ Yes' : '✗ No') . "</td>";
                    echo "<td>" . ($size > 0 ? number_format($size) . ' bytes' : '0 bytes') . "</td>";
                    echo "<td>" . ($readable ? '✓ Yes' : '✗ No') . "</td>";
                    echo "<td>{$fullPath}</td>";
                    echo "</tr>";
                }
                ?>
            </table>
        </div>
        
        <!-- URL Generation Test -->
        <?php if ($functionsLoaded): ?>
        <div class="debug-section">
            <h2>Generated URLs</h2>
            <table>
                <tr><th>File</th><th>Generated URL</th><th>Test Link</th></tr>
                <?php
                $urlFiles = [
                    'style.css' => 'assets/css/style.css',
                    'dark-mode.css' => 'assets/css/dark-mode.css',
                    'bootstrap.css' => 'assets/vendor/bootstrap/css/bootstrap.min.css',
                    'fontawesome.css' => 'assets/vendor/fontawesome/css/all.min.css'
                ];
                
                foreach ($urlFiles as $name => $path) {
                    $url = getBaseUrl() . '/' . $path;
                    echo "<tr>";
                    echo "<td>{$name}</td>";
                    echo "<td><code>{$url}</code></td>";
                    echo "<td><a href='{$url}' target='_blank' class='test-link'>Test</a></td>";
                    echo "</tr>";
                }
                ?>
            </table>
        </div>
        <?php endif; ?>
        
        <!-- Direct Path Test -->
        <div class="debug-section">
            <h2>Direct Path Test</h2>
            <p>Test these direct paths (relative to current directory):</p>
            <?php
            $directPaths = [
                './assets/css/style.css',
                './assets/css/dark-mode.css'
            ];
            
            foreach ($directPaths as $path) {
                echo "<a href='{$path}' target='_blank' class='test-link'>{$path}</a>";
            }
            ?>
        </div>
        
        <!-- CSS Content Preview -->
        <div class="debug-section">
            <h2>CSS File Content Preview</h2>
            <?php
            $mainCSS = 'assets/css/style.css';
            if (file_exists($mainCSS)) {
                $content = file_get_contents($mainCSS);
                $preview = substr($content, 0, 500);
                echo "<h3>style.css (first 500 characters):</h3>";
                echo "<div class='code'>" . htmlspecialchars($preview) . "...</div>";
                echo "<p><strong>Total size:</strong> " . number_format(strlen($content)) . " characters</p>";
            } else {
                echo "<p class='error'>style.css not found!</p>";
            }
            ?>
        </div>
        
        <!-- Browser Test -->
        <div class="debug-section">
            <h2>Browser CSS Test</h2>
            <p>Testing inline CSS to verify browser CSS support:</p>
            
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin: 10px 0;">
                <h3 style="margin: 0;">Gradient Background Test</h3>
                <p style="margin: 5px 0;">If you can see this purple gradient background, CSS is working in your browser.</p>
            </div>
            
            <div style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); padding: 20px; border-radius: 10px; border: 1px solid rgba(255, 255, 255, 0.2);">
                <h3 style="margin: 0;">Glass Effect Test</h3>
                <p style="margin: 5px 0;">If you can see a glass/blur effect here, advanced CSS is working.</p>
            </div>
        </div>
        
        <!-- Recommendations -->
        <div class="debug-section warning">
            <h2>Troubleshooting Steps</h2>
            <ol>
                <li><strong>Test the minimal page:</strong> <a href="minimal_test.html" class="test-link">Open Minimal Test</a></li>
                <li><strong>Check direct CSS access:</strong> Click the "Test" links above</li>
                <li><strong>Verify file permissions:</strong> Ensure CSS files are readable</li>
                <li><strong>Clear browser cache:</strong> Press Ctrl+F5 or use incognito mode</li>
                <li><strong>Check .htaccess:</strong> Ensure static files aren't being redirected</li>
            </ol>
        </div>
    </div>
    
    <script>
        console.log('Debug page loaded');
        console.log('Check the console for any CSS loading errors');
        
        // Test if we can detect CSS loading
        const testElement = document.createElement('div');
        testElement.style.display = 'none';
        testElement.className = 'test-css-loading';
        document.body.appendChild(testElement);
        
        // Try to load a CSS file dynamically
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = './assets/css/style.css';
        link.onload = function() {
            console.log('✓ CSS file loaded successfully');
        };
        link.onerror = function() {
            console.log('✗ CSS file failed to load');
        };
        document.head.appendChild(link);
    </script>
</body>
</html>
