<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Local Assets Test - Medical Device Management</title>
    
    <!-- Local Bootstrap CSS -->
    <link rel="stylesheet" href="./assets/vendor/bootstrap/css/bootstrap.min.css">
    
    <!-- Local Font Awesome -->
    <link rel="stylesheet" href="./assets/vendor/fontawesome/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="./assets/css/style.css">
    <link rel="stylesheet" href="./assets/css/dark-mode.css">
    
    <style>
        .test-status {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            border: 2px solid;
        }
        .test-pass {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .test-fail {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-dark sidebar">
                <div class="position-sticky pt-3 sidebar-sticky">
                    <div class="text-center mb-4">
                        <h5 class="text-white">Local Assets Test</h5>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#">
                                <i class="fas fa-check-circle me-2"></i>Local Test
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-hospital me-2"></i>Hospitals
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 text-gradient mb-1">Local Assets Test</h1>
                        <p class="text-muted mb-0">Testing local Bootstrap and FontAwesome files</p>
                    </div>
                </div>
                
                <!-- Asset Status Check -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5>Bootstrap CSS</h5>
                                <?php if (file_exists('./assets/vendor/bootstrap/css/bootstrap.min.css')): ?>
                                    <i class="fas fa-check-circle fa-2x text-success"></i>
                                    <p class="text-success mt-2">File exists</p>
                                <?php else: ?>
                                    <i class="fas fa-times-circle fa-2x text-danger"></i>
                                    <p class="text-danger mt-2">File missing</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5>FontAwesome CSS</h5>
                                <?php if (file_exists('./assets/vendor/fontawesome/css/all.min.css')): ?>
                                    <i class="fas fa-check-circle fa-2x text-success"></i>
                                    <p class="text-success mt-2">File exists</p>
                                <?php else: ?>
                                    <i class="fas fa-times-circle fa-2x text-danger"></i>
                                    <p class="text-danger mt-2">File missing</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5>Custom CSS</h5>
                                <?php if (file_exists('./assets/css/style.css')): ?>
                                    <i class="fas fa-check-circle fa-2x text-success"></i>
                                    <p class="text-success mt-2">File exists</p>
                                <?php else: ?>
                                    <i class="fas fa-times-circle fa-2x text-danger"></i>
                                    <p class="text-danger mt-2">File missing</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Test Results -->
                <div id="test-results">
                    <h3>Runtime Test Results:</h3>
                    <div id="bootstrap-test" class="test-status test-fail">
                        <strong>Bootstrap Test:</strong> <span id="bootstrap-result">Testing...</span>
                    </div>
                    <div id="fontawesome-test" class="test-status test-fail">
                        <strong>FontAwesome Test:</strong> <span id="fontawesome-result">Testing...</span>
                    </div>
                    <div id="custom-css-test" class="test-status test-fail">
                        <strong>Custom CSS Test:</strong> <span id="custom-css-result">Testing...</span>
                    </div>
                </div>
                
                <!-- Bootstrap Components Test -->
                <div class="alert alert-success fade-in-up">
                    <i class="fas fa-check-circle me-2"></i>
                    This alert should be green if Bootstrap is working
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    This alert should be blue if Bootstrap is working
                </div>
                
                <!-- Custom CSS Test -->
                <div class="card card-glass fade-in-up mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-vial me-2"></i>Glass Effect Test
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>This card should have a glass/blur effect if custom CSS is working.</p>
                    </div>
                </div>
                
                <!-- Stats Cards -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card stats-card primary h-100 fade-in-up">
                            <div class="card-body text-center">
                                <div class="stats-icon mb-3">
                                    <i class="fas fa-download fa-2x text-primary"></i>
                                </div>
                                <div class="stats-number">Local</div>
                                <div class="stats-text">Assets</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card stats-card success h-100 fade-in-up">
                            <div class="card-body text-center">
                                <div class="stats-icon mb-3">
                                    <i class="fas fa-check fa-2x text-success"></i>
                                </div>
                                <div class="stats-number">Test</div>
                                <div class="stats-text">Complete</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Download Assets Button -->
                <?php if (!file_exists('./assets/vendor/bootstrap/css/bootstrap.min.css')): ?>
                <div class="alert alert-warning mt-4">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>Assets Missing</h5>
                    <p>Local assets are not downloaded yet. Click the button below to download them.</p>
                    <a href="download_assets.php" class="btn btn-warning">
                        <i class="fas fa-download me-2"></i>Download Local Assets
                    </a>
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>
    
    <!-- Local Bootstrap JS -->
    <script src="./assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Testing local assets...');
            
            // Test Bootstrap
            const alertElement = document.querySelector('.alert-success');
            const alertStyles = window.getComputedStyle(alertElement);
            const alertBg = alertStyles.backgroundColor;
            
            console.log('Alert background:', alertBg);
            
            if (alertBg.includes('212, 237, 218') || alertBg.includes('rgb(212, 237, 218)') || alertBg === 'rgb(212, 237, 218)') {
                document.getElementById('bootstrap-test').className = 'test-status test-pass';
                document.getElementById('bootstrap-result').textContent = 'PASS - Bootstrap is working';
            } else {
                document.getElementById('bootstrap-result').textContent = 'FAIL - Bootstrap not working (bg: ' + alertBg + ')';
            }
            
            // Test FontAwesome
            const iconElement = document.querySelector('.fas.fa-check-circle');
            const iconStyles = window.getComputedStyle(iconElement, ':before');
            const fontFamily = iconStyles.fontFamily;
            
            console.log('Icon font family:', fontFamily);
            
            if (fontFamily.includes('Font Awesome') || fontFamily.includes('FontAwesome')) {
                document.getElementById('fontawesome-test').className = 'test-status test-pass';
                document.getElementById('fontawesome-result').textContent = 'PASS - FontAwesome is working';
            } else {
                document.getElementById('fontawesome-result').textContent = 'FAIL - FontAwesome not working (font: ' + fontFamily + ')';
            }
            
            // Test Custom CSS
            const glassCard = document.querySelector('.card-glass');
            if (glassCard) {
                const glassStyles = window.getComputedStyle(glassCard);
                const backdropFilter = glassStyles.backdropFilter || glassStyles.webkitBackdropFilter;
                
                console.log('Glass backdrop filter:', backdropFilter);
                
                if (backdropFilter && backdropFilter !== 'none') {
                    document.getElementById('custom-css-test').className = 'test-status test-pass';
                    document.getElementById('custom-css-result').textContent = 'PASS - Custom CSS is working';
                } else {
                    document.getElementById('custom-css-result').textContent = 'FAIL - Custom CSS not working (backdrop-filter: ' + backdropFilter + ')';
                }
            }
        });
    </script>
</body>
</html>
