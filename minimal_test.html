<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal CSS Test</title>
    
    <!-- Inline Bootstrap CSS for testing -->
    <style>
        /* Minimal Bootstrap-like styles for testing */
        .container-fluid { width: 100%; padding: 0 15px; }
        .row { display: flex; flex-wrap: wrap; margin: 0 -15px; }
        .col-md-3 { flex: 0 0 25%; max-width: 25%; padding: 0 15px; }
        .col-md-9 { flex: 0 0 75%; max-width: 75%; padding: 0 15px; }
        
        .bg-dark { background-color: #343a40 !important; }
        .text-white { color: #fff !important; }
        .text-muted { color: #6c757d !important; }
        
        .alert {
            padding: 0.75rem 1.25rem;
            margin-bottom: 1rem;
            border: 1px solid transparent;
            border-radius: 0.375rem;
        }
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .alert-info {
            color: #0c5460;
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        .alert-warning {
            color: #856404;
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        
        .card {
            position: relative;
            display: flex;
            flex-direction: column;
            min-width: 0;
            word-wrap: break-word;
            background-color: #fff;
            background-clip: border-box;
            border: 1px solid rgba(0,0,0,.125);
            border-radius: 0.375rem;
            margin-bottom: 1rem;
        }
        .card-header {
            padding: 0.75rem 1.25rem;
            margin-bottom: 0;
            background-color: rgba(0,0,0,.03);
            border-bottom: 1px solid rgba(0,0,0,.125);
        }
        .card-body {
            flex: 1 1 auto;
            padding: 1.25rem;
        }
        
        .btn {
            display: inline-block;
            font-weight: 400;
            text-align: center;
            vertical-align: middle;
            cursor: pointer;
            border: 1px solid transparent;
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: 0.375rem;
            text-decoration: none;
            color: #212529;
        }
        .btn-primary {
            color: #fff;
            background-color: #007bff;
            border-color: #007bff;
        }
        .btn-success {
            color: #fff;
            background-color: #28a745;
            border-color: #28a745;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            width: 240px;
        }
        
        .main-content {
            margin-left: 240px;
            padding: 20px;
        }
        
        .nav {
            display: flex;
            flex-wrap: wrap;
            padding-left: 0;
            margin-bottom: 0;
            list-style: none;
            flex-direction: column;
        }
        
        .nav-link {
            display: block;
            padding: 0.5rem 1rem;
            color: rgba(255,255,255,.75);
            text-decoration: none;
        }
        
        .nav-link:hover {
            color: rgba(255,255,255,1);
        }
        
        .nav-link.active {
            color: #fff;
            background-color: #007bff;
        }
        
        /* Custom styles from your CSS */
        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .text-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .card-glass {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .stats-card {
            border: none;
            border-radius: 1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            background: #fff;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
        
        .stats-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.25rem;
            line-height: 1;
        }
        
        .stats-text {
            color: #6c757d;
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .text-center { text-align: center; }
        .mb-1 { margin-bottom: 0.25rem; }
        .mb-2 { margin-bottom: 0.5rem; }
        .mb-3 { margin-bottom: 1rem; }
        .mb-4 { margin-bottom: 1.5rem; }
        .mt-4 { margin-top: 1.5rem; }
        .p-3 { padding: 1rem; }
        .h-100 { height: 100%; }
        
        /* FontAwesome replacement */
        .fas::before {
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }
        
        .fa-check-circle::before { content: '✓'; }
        .fa-hospital::before { content: '🏥'; }
        .fa-microscope::before { content: '🔬'; }
        .fa-vial::before { content: '🧪'; }
        .fa-download::before { content: '⬇'; }
        .fa-check::before { content: '✓'; }
        .fa-info-circle::before { content: 'ℹ'; }
        .fa-exclamation-triangle::before { content: '⚠'; }
        
        .me-2 { margin-right: 0.5rem; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 bg-dark sidebar">
                <div class="text-center mb-4" style="padding-top: 1rem;">
                    <h5 class="text-white">Minimal Test</h5>
                </div>
                
                <ul class="nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="#">
                            <span class="fas fa-check-circle me-2"></span>Minimal Test
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <span class="fas fa-hospital me-2"></span>Hospitals
                        </a>
                    </li>
                </ul>
            </nav>
            
            <!-- Main content -->
            <main class="col-md-9 main-content">
                <div class="mb-4">
                    <h1 class="text-gradient mb-1">Minimal CSS Test</h1>
                    <p class="text-muted mb-0">Testing with inline CSS to isolate issues</p>
                </div>
                
                <!-- Test Results -->
                <div class="alert alert-success">
                    <span class="fas fa-check-circle me-2"></span>
                    If you can see this GREEN alert with proper styling, the basic CSS is working!
                </div>
                
                <div class="alert alert-info">
                    <span class="fas fa-info-circle me-2"></span>
                    If you can see this BLUE alert with proper styling, the CSS is definitely working!
                </div>
                
                <div class="alert alert-warning">
                    <span class="fas fa-exclamation-triangle me-2"></span>
                    This should be a YELLOW/ORANGE warning alert.
                </div>
                
                <!-- Custom CSS Test -->
                <div class="card card-glass fade-in-up mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <span class="fas fa-vial me-2"></span>Glass Effect Test
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>This card should have a glass/blur effect and smooth animation.</p>
                        <button class="btn btn-primary">
                            <span class="fas fa-check me-2"></span>Test Button
                        </button>
                    </div>
                </div>
                
                <!-- Stats Cards -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="card stats-card h-100 fade-in-up">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <span class="fas fa-download" style="font-size: 2rem; color: #007bff;"></span>
                                </div>
                                <div class="stats-number">100%</div>
                                <div class="stats-text">Working</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card h-100 fade-in-up">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <span class="fas fa-check" style="font-size: 2rem; color: #28a745;"></span>
                                </div>
                                <div class="stats-number">CSS</div>
                                <div class="stats-text">Loaded</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Diagnostic Info -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Diagnostic Information</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>This test uses:</strong></p>
                        <ul>
                            <li>✓ Inline CSS (no external dependencies)</li>
                            <li>✓ Basic Bootstrap-like styles</li>
                            <li>✓ Custom animations and effects</li>
                            <li>✓ Emoji icons instead of FontAwesome</li>
                        </ul>
                        
                        <p><strong>If this page looks styled correctly, then:</strong></p>
                        <ul>
                            <li>Your browser supports CSS</li>
                            <li>The issue is with external file loading</li>
                            <li>We need to fix the file paths or routing</li>
                        </ul>
                        
                        <div class="mt-3">
                            <button class="btn btn-success" onclick="testComplete()">
                                <span class="fas fa-check me-2"></span>This Page Looks Good
                            </button>
                            <button class="btn btn-primary" onclick="testFailed()">
                                <span class="fas fa-exclamation-triangle me-2"></span>This Page Looks Broken
                            </button>
                        </div>
                        
                        <div id="test-result" style="margin-top: 1rem; padding: 1rem; border-radius: 0.375rem; display: none;"></div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script>
        function testComplete() {
            const result = document.getElementById('test-result');
            result.style.display = 'block';
            result.style.backgroundColor = '#d4edda';
            result.style.color = '#155724';
            result.style.border = '1px solid #c3e6cb';
            result.innerHTML = `
                <h5>✓ CSS is working!</h5>
                <p>Since this page displays correctly, the issue is with external file loading. 
                The problem is likely:</p>
                <ul>
                    <li>File paths in your main application</li>
                    <li>URL routing issues</li>
                    <li>Server configuration</li>
                </ul>
                <p><strong>Next step:</strong> We need to fix the file paths in your main application.</p>
            `;
        }
        
        function testFailed() {
            const result = document.getElementById('test-result');
            result.style.display = 'block';
            result.style.backgroundColor = '#f8d7da';
            result.style.color = '#721c24';
            result.style.border = '1px solid #f5c6cb';
            result.innerHTML = `
                <h5>✗ CSS is not working</h5>
                <p>If this page doesn't look styled, there might be a deeper issue:</p>
                <ul>
                    <li>Browser CSS support disabled</li>
                    <li>Browser extensions blocking CSS</li>
                    <li>Server not serving CSS content-type correctly</li>
                </ul>
                <p><strong>Try:</strong> Open this page in a different browser or incognito mode.</p>
            `;
        }
        
        console.log('Minimal test page loaded');
        console.log('If you can see styled content, CSS is working');
    </script>
</body>
</html>
