<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Test</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/dark-mode.css">
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-primary">CSS Loading Test</h1>
        
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            If you can see this green alert with an icon, <PERSON>tra<PERSON> and FontAwesome are loading correctly.
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Test Card</h5>
            </div>
            <div class="card-body">
                <p>This is a test card to verify Bootstrap styling is working.</p>
                <button class="btn btn-primary">
                    <i class="fas fa-test me-2"></i>Test Button
                </button>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>Custom CSS Test</h3>
            <div class="card-glass p-3 mb-3">
                <p>This should have a glass effect if custom CSS is loading.</p>
            </div>
            
            <div class="stats-card primary p-3">
                <p>This should have enhanced styling if custom CSS is loading.</p>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
