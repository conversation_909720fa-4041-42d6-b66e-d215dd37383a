<?php
/**
 * Debug CSS Loading Issues
 * 
 * This file helps debug CSS loading problems
 */

// Start session
session_start();

// Include necessary files
require_once 'includes/functions.php';

echo "<h1>CSS Debug Information</h1>";

// Check base URL
echo "<h2>Base URL Information:</h2>";
echo "<p><strong>getBaseUrl():</strong> " . getBaseUrl() . "</p>";
echo "<p><strong>Current Script:</strong> " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>HTTP Host:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";

// Check CSS file paths
echo "<h2>CSS File Paths:</h2>";
$cssFiles = [
    'style.css' => getBaseUrl() . '/assets/css/style.css',
    'dark-mode.css' => getBaseUrl() . '/assets/css/dark-mode.css'
];

foreach ($cssFiles as $name => $url) {
    echo "<p><strong>{$name}:</strong> <a href='{$url}' target='_blank'>{$url}</a></p>";
    
    // Check if file exists locally
    $localPath = __DIR__ . '/assets/css/' . $name;
    echo "<p>Local file exists: " . (file_exists($localPath) ? 'YES' : 'NO') . " ({$localPath})</p>";
    
    // Try to fetch the file
    $headers = @get_headers($url);
    $status = $headers ? $headers[0] : 'Could not fetch headers';
    echo "<p>HTTP Status: {$status}</p>";
    echo "<hr>";
}

// Check external CSS
echo "<h2>External CSS (Bootstrap, FontAwesome):</h2>";
$externalCSS = [
    'Bootstrap' => 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
    'FontAwesome' => 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
];

foreach ($externalCSS as $name => $url) {
    echo "<p><strong>{$name}:</strong> <a href='{$url}' target='_blank'>{$url}</a></p>";
    $headers = @get_headers($url);
    $status = $headers ? $headers[0] : 'Could not fetch headers';
    echo "<p>HTTP Status: {$status}</p>";
    echo "<hr>";
}

// Test simple HTML with inline CSS
echo "<h2>Test Styling:</h2>";
echo '<div style="background-color: #007bff; color: white; padding: 10px; border-radius: 5px;">This should be blue with white text</div>';

// Check if we can load Bootstrap
echo "<h2>Bootstrap Test:</h2>";
echo '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">';
echo '<div class="alert alert-success">If this appears as a green alert box, Bootstrap is loading correctly</div>';
?>
