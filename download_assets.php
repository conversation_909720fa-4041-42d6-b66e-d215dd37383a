<?php
/**
 * Download Bootstrap and FontAwesome assets locally
 * This script downloads external CSS/JS files to local assets folder
 */

// Create directories if they don't exist
$directories = [
    'assets/vendor/bootstrap/css',
    'assets/vendor/bootstrap/js',
    'assets/vendor/fontawesome/css',
    'assets/vendor/fontawesome/webfonts'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "Created directory: {$dir}<br>";
    }
}

// Files to download
$files = [
    // Bootstrap CSS
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' => 'assets/vendor/bootstrap/css/bootstrap.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' => 'assets/vendor/bootstrap/css/bootstrap.rtl.min.css',
    
    // Bootstrap JS
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js' => 'assets/vendor/bootstrap/js/bootstrap.bundle.min.js',
    
    // FontAwesome CSS
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' => 'assets/vendor/fontawesome/css/all.min.css',
    
    // jQuery
    'https://code.jquery.com/jquery-3.6.0.min.js' => 'assets/vendor/jquery/jquery-3.6.0.min.js'
];

echo "<h1>Downloading Assets</h1>";

foreach ($files as $url => $localPath) {
    echo "<p>Downloading: {$url}</p>";
    
    // Create directory if it doesn't exist
    $dir = dirname($localPath);
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
    
    // Download file
    $content = @file_get_contents($url);
    
    if ($content !== false) {
        file_put_contents($localPath, $content);
        echo "<p style='color: green;'>✓ Downloaded to: {$localPath} (" . number_format(strlen($content)) . " bytes)</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to download: {$url}</p>";
    }
    
    echo "<hr>";
}

// Download FontAwesome webfonts
$fontAwesomeCSS = file_get_contents('assets/vendor/fontawesome/css/all.min.css');
if ($fontAwesomeCSS) {
    // Extract font URLs from CSS
    preg_match_all('/url\((.*?)\)/', $fontAwesomeCSS, $matches);
    
    foreach ($matches[1] as $fontUrl) {
        $fontUrl = trim($fontUrl, '"\'');
        if (strpos($fontUrl, 'http') === 0) {
            $fontName = basename($fontUrl);
            $localFontPath = 'assets/vendor/fontawesome/webfonts/' . $fontName;
            
            echo "<p>Downloading font: {$fontUrl}</p>";
            $fontContent = @file_get_contents($fontUrl);
            
            if ($fontContent !== false) {
                file_put_contents($localFontPath, $fontContent);
                echo "<p style='color: green;'>✓ Downloaded font: {$localFontPath}</p>";
                
                // Update CSS to use local path
                $fontAwesomeCSS = str_replace($fontUrl, '../webfonts/' . $fontName, $fontAwesomeCSS);
            } else {
                echo "<p style='color: red;'>✗ Failed to download font: {$fontUrl}</p>";
            }
        }
    }
    
    // Save updated CSS
    file_put_contents('assets/vendor/fontawesome/css/all.min.css', $fontAwesomeCSS);
    echo "<p style='color: blue;'>Updated FontAwesome CSS with local font paths</p>";
}

echo "<h2>Download Complete!</h2>";
echo "<p>You can now use local assets instead of CDN.</p>";
echo "<p><a href='test_local_assets.php'>Test Local Assets</a></p>";
?>
