<?php
/**
 * Fix Translations Script
 * This script replaces translation functions with simple English text
 */

$file = 'views/hospitals/index.php';
$content = file_get_contents($file);

// Define translation replacements
$translations = [
    "__('add_hospital')" => "'Add Hospital'",
    "__('export')" => "'Export'",
    "__('export_formats')" => "'Export Formats'",
    "__('report')" => "'Report'",
    "__('spreadsheet')" => "'Spreadsheet'",
    "__('data')" => "'Data'",
    "__('print_list')" => "'Print List'",
    "__('total_hospitals')" => "'Total Hospitals'",
    "__('total_departments')" => "'Total Departments'",
    "__('total_devices')" => "'Total Devices'",
    "__('countries')" => "'Countries'",
    "__('hospitals_list')" => "'Hospitals List'",
    "__('table')" => "'Table'",
    "__('cards')" => "'Cards'",
    "__('hospital')" => "'Hospital'",
    "__('location')" => "'Location'",
    "__('contact')" => "'Contact'",
    "__('departments')" => "'Departments'",
    "__('devices')" => "'Devices'",
    "__('actions')" => "'Actions'",
    "__('no_hospitals')" => "'No Hospitals'",
    "__('no_hospitals_found_message')" => "'No hospitals found in the system'",
    "__('add_first_hospital')" => "'Add First Hospital'",
    "__('view_hospital')" => "'View Hospital'",
    "__('edit_hospital')" => "'Edit Hospital'",
    "__('delete_hospital')" => "'Delete Hospital'",
    "__('view')" => "'View'",
    "__('export_options')" => "'Export Options'",
    "__('export_format')" => "'Export Format'",
    "__('confirm_delete')" => "'Confirm Delete'",
    "__('confirm_delete_hospital')" => "'Are you sure you want to delete hospital'",
    "__('delete_hospital_warning')" => "'This action cannot be undone and will remove all associated data'",
    "__('cancel')" => "'Cancel'",
    "__('delete')" => "'Delete'",
    "__('search')" => "'Search'",
    "__('show')" => "'Show'",
    "__('entries')" => "'entries'",
    "__('showing')" => "'Showing'",
    "__('to')" => "'to'",
    "__('of')" => "'of'",
    "__('first')" => "'First'",
    "__('last')" => "'Last'",
    "__('next')" => "'Next'",
    "__('previous')" => "'Previous'",
    "__('export_started')" => "'Export started'"
];

// Replace translations
foreach ($translations as $search => $replace) {
    $content = str_replace($search, $replace, $content);
}

// Write back to file
file_put_contents($file, $content);

echo "Translation functions replaced with simple text in $file\n";
echo "Replacements made: " . count($translations) . "\n";

// Test the file for syntax errors
$output = [];
$return_var = 0;
exec("php -l $file 2>&1", $output, $return_var);

if ($return_var === 0) {
    echo "✓ File syntax is valid\n";
} else {
    echo "✗ Syntax errors found:\n";
    echo implode("\n", $output) . "\n";
}

echo "\nYou can now test the hospitals page at: http://localhost/bio/hospitals\n";
?>
