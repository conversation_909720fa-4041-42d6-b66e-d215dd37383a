<?php
/**
 * Debug Hospitals Page
 * This page helps debug hospital data issues
 */

// Include the configuration and functions
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('login');
}

// Get models
require_once 'models/Hospital.php';
require_once 'models/Department.php';
require_once 'models/Device.php';

$hospitalModel = new Hospital();
$departmentModel = new Department();
$deviceModel = new Device();

// Get current user
$currentUser = getCurrentUser();
$userHospitalId = $currentUser['hospital_id'];

echo "<h1>Hospital Debug Information</h1>";

echo "<h2>Current User Info:</h2>";
echo "<pre>";
print_r($currentUser);
echo "</pre>";

echo "<h2>User Hospital ID: " . ($userHospitalId ?: 'None') . "</h2>";

echo "<h2>User Permissions:</h2>";
echo "<ul>";
echo "<li>view_hospitals: " . (hasPermission('view_hospitals') ? 'Yes' : 'No') . "</li>";
echo "<li>manage_hospitals: " . (hasPermission('manage_hospitals') ? 'Yes' : 'No') . "</li>";
echo "</ul>";

try {
    echo "<h2>All Hospitals from Database:</h2>";
    $allHospitals = $hospitalModel->getAll();
    echo "<p>Count: " . count($allHospitals) . "</p>";
    echo "<pre>";
    print_r($allHospitals);
    echo "</pre>";
    
    if ($userHospitalId) {
        echo "<h2>User's Hospital:</h2>";
        $userHospital = $hospitalModel->getById($userHospitalId);
        echo "<pre>";
        print_r($userHospital);
        echo "</pre>";
    }
    
    echo "<h2>Hospitals with Enhanced Data:</h2>";
    $hospitals = $allHospitals;
    
    // Add additional data for each hospital
    foreach ($hospitals as &$hospital) {
        // Add department count
        try {
            $hospital['department_count'] = $departmentModel->countByHospital($hospital['id']);
        } catch (Exception $e) {
            $hospital['department_count'] = 0;
            echo "<p>Error getting department count for hospital {$hospital['id']}: " . $e->getMessage() . "</p>";
        }
        
        // Add device count
        try {
            $hospital['device_count'] = $deviceModel->countByHospital($hospital['id']);
        } catch (Exception $e) {
            $hospital['device_count'] = 0;
            echo "<p>Error getting device count for hospital {$hospital['id']}: " . $e->getMessage() . "</p>";
        }
        
        // Add default values if missing
        if (!isset($hospital['city'])) $hospital['city'] = '';
        if (!isset($hospital['country'])) $hospital['country'] = '';
        if (!isset($hospital['email'])) $hospital['email'] = '';
    }
    
    echo "<pre>";
    print_r($hospitals);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<h2>Error:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>";
    print_r($e->getTrace());
    echo "</pre>";
}

echo "<h2>Database Connection Test:</h2>";
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    echo "<p style='color: green;'>Database connection successful</p>";
    
    // Check if hospitals table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'hospitals'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>Hospitals table exists</p>";
        
        // Check table structure
        $stmt = $pdo->query("DESCRIBE hospitals");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<h3>Hospitals Table Structure:</h3>";
        echo "<pre>";
        print_r($columns);
        echo "</pre>";
        
        // Check data
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM hospitals");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>Total hospitals in database: " . $count['count'] . "</p>";
        
        if ($count['count'] > 0) {
            $stmt = $pdo->query("SELECT * FROM hospitals LIMIT 5");
            $sampleData = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<h3>Sample Hospital Data:</h3>";
            echo "<pre>";
            print_r($sampleData);
            echo "</pre>";
        }
    } else {
        echo "<p style='color: red;'>Hospitals table does not exist</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Database connection failed: " . $e->getMessage() . "</p>";
}

echo "<h2>Model Files Check:</h2>";
echo "<ul>";
echo "<li>Hospital.php: " . (file_exists('models/Hospital.php') ? 'Exists' : 'Missing') . "</li>";
echo "<li>Department.php: " . (file_exists('models/Department.php') ? 'Exists' : 'Missing') . "</li>";
echo "<li>Device.php: " . (file_exists('models/Device.php') ? 'Exists' : 'Missing') . "</li>";
echo "</ul>";

echo "<h2>Asset Files Check:</h2>";
echo "<ul>";
echo "<li>Bootstrap CSS: " . (file_exists('assets/vendor/bootstrap/css/bootstrap.min.css') ? 'Exists' : 'Missing') . "</li>";
echo "<li>FontAwesome CSS: " . (file_exists('assets/vendor/fontawesome/css/all.min.css') ? 'Exists' : 'Missing') . "</li>";
echo "<li>Custom CSS: " . (file_exists('assets/css/style.css') ? 'Exists' : 'Missing') . "</li>";
echo "<li>jQuery JS: " . (file_exists('assets/vendor/jquery/jquery-3.6.0.min.js') ? 'Exists' : 'Missing') . "</li>";
echo "</ul>";

echo "<h2>Quick Test Links:</h2>";
echo "<ul>";
echo "<li><a href='" . getBaseUrl() . "/hospitals'>Go to Hospitals Page</a></li>";
echo "<li><a href='" . getBaseUrl() . "/dashboard'>Go to Dashboard</a></li>";
echo "<li><a href='" . getBaseUrl() . "/asset_diagnostic.php'>Asset Diagnostic</a></li>";
echo "</ul>";
?>
