<?php
/**
 * Asset Diagnostic Tool
 * This script checks if all assets are properly loaded and accessible
 */

// Include functions
require_once 'includes/functions.php';

// Get base URL
$baseUrl = getBaseUrl();

// Define asset files to check
$assets = [
    'Bootstrap CSS' => 'assets/vendor/bootstrap/css/bootstrap.min.css',
    'Bootstrap RTL CSS' => 'assets/vendor/bootstrap/css/bootstrap.rtl.min.css',
    'Bootstrap JS' => 'assets/vendor/bootstrap/js/bootstrap.bundle.min.js',
    'FontAwesome CSS' => 'assets/vendor/fontawesome/css/all.min.css',
    'jQuery' => 'assets/vendor/jquery/jquery-3.6.0.min.js',
    'Custom CSS' => 'assets/css/style.css',
    'Dark Mode CSS' => 'assets/css/dark-mode.css'
];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asset Diagnostic - Medical Device Management System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
        }
        .status-pass {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .status-fail {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .status-icon {
            font-size: 24px;
            margin-right: 10px;
        }
        .pass { color: #28a745; }
        .fail { color: #dc3545; }
        .info-section {
            margin-top: 30px;
            padding: 20px;
            background: #e9ecef;
            border-radius: 8px;
        }
        .url-test {
            font-family: monospace;
            background: #f1f3f4;
            padding: 5px;
            border-radius: 4px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 Medical Device Management System</h1>
            <h2>Asset Diagnostic Report</h2>
            <p><strong>Base URL:</strong> <span class="url-test"><?php echo htmlspecialchars($baseUrl); ?></span></p>
            <p><strong>Generated at:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>

        <div class="status-grid">
            <?php foreach ($assets as $name => $path): ?>
                <?php
                $fullPath = __DIR__ . '/' . $path;
                $exists = file_exists($fullPath);
                $readable = $exists ? is_readable($fullPath) : false;
                $size = $exists ? filesize($fullPath) : 0;
                $url = $baseUrl . '/' . $path;
                ?>
                <div class="status-card <?php echo $exists && $readable && $size > 0 ? 'status-pass' : 'status-fail'; ?>">
                    <h3>
                        <span class="status-icon <?php echo $exists && $readable && $size > 0 ? 'pass' : 'fail'; ?>">
                            <?php echo $exists && $readable && $size > 0 ? '✅' : '❌'; ?>
                        </span>
                        <?php echo htmlspecialchars($name); ?>
                    </h3>
                    <p><strong>Path:</strong> <code><?php echo htmlspecialchars($path); ?></code></p>
                    <p><strong>Full Path:</strong> <code><?php echo htmlspecialchars($fullPath); ?></code></p>
                    <p><strong>URL:</strong> <a href="<?php echo htmlspecialchars($url); ?>" target="_blank" class="url-test"><?php echo htmlspecialchars($url); ?></a></p>
                    <p><strong>Exists:</strong> <?php echo $exists ? '✅ Yes' : '❌ No'; ?></p>
                    <p><strong>Readable:</strong> <?php echo $readable ? '✅ Yes' : '❌ No'; ?></p>
                    <p><strong>Size:</strong> <?php echo $size > 0 ? number_format($size) . ' bytes' : '0 bytes'; ?></p>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="info-section">
            <h3>📊 Summary</h3>
            <?php
            $totalAssets = count($assets);
            $workingAssets = 0;
            foreach ($assets as $name => $path) {
                $fullPath = __DIR__ . '/' . $path;
                if (file_exists($fullPath) && is_readable($fullPath) && filesize($fullPath) > 0) {
                    $workingAssets++;
                }
            }
            $percentage = round(($workingAssets / $totalAssets) * 100, 1);
            ?>
            <p><strong>Total Assets:</strong> <?php echo $totalAssets; ?></p>
            <p><strong>Working Assets:</strong> <?php echo $workingAssets; ?></p>
            <p><strong>Success Rate:</strong> <?php echo $percentage; ?>%</p>
            
            <?php if ($percentage == 100): ?>
                <div style="color: #28a745; font-weight: bold; font-size: 18px;">
                    🎉 All assets are working correctly!
                </div>
            <?php elseif ($percentage >= 80): ?>
                <div style="color: #ffc107; font-weight: bold; font-size: 18px;">
                    ⚠️ Most assets are working, but some issues detected.
                </div>
            <?php else: ?>
                <div style="color: #dc3545; font-weight: bold; font-size: 18px;">
                    🚨 Multiple asset loading issues detected!
                </div>
            <?php endif; ?>
        </div>

        <div class="info-section">
            <h3>🔧 Server Information</h3>
            <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
            <p><strong>Server Software:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
            <p><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?></p>
            <p><strong>Script Path:</strong> <?php echo $_SERVER['SCRIPT_NAME'] ?? 'Unknown'; ?></p>
            <p><strong>Current Directory:</strong> <?php echo __DIR__; ?></p>
        </div>

        <div class="info-section">
            <h3>🌐 URL Testing</h3>
            <p>Click on the asset URLs above to test if they're accessible via HTTP.</p>
            <p>If assets show as existing but URLs don't work, there might be a server configuration issue.</p>
        </div>
    </div>
</body>
</html>
