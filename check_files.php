<?php
/**
 * File Access Checker
 * 
 * This script checks if CSS and other asset files are accessible
 */

echo "<h1>File Access Check</h1>";

// Define files to check
$filesToCheck = [
    'assets/css/style.css',
    'assets/css/dark-mode.css',
    'includes/functions.php',
    'config/database.php',
    '.htaccess'
];

echo "<h2>File Existence and Permissions:</h2>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>File</th><th>Exists</th><th>Readable</th><th>Size</th><th>Permissions</th></tr>";

foreach ($filesToCheck as $file) {
    $exists = file_exists($file);
    $readable = $exists ? is_readable($file) : false;
    $size = $exists ? filesize($file) : 0;
    $perms = $exists ? substr(sprintf('%o', fileperms($file)), -4) : 'N/A';
    
    echo "<tr>";
    echo "<td>{$file}</td>";
    echo "<td>" . ($exists ? 'YES' : 'NO') . "</td>";
    echo "<td>" . ($readable ? 'YES' : 'NO') . "</td>";
    echo "<td>" . ($size > 0 ? number_format($size) . ' bytes' : '0 bytes') . "</td>";
    echo "<td>{$perms}</td>";
    echo "</tr>";
}

echo "</table>";

// Check CSS file contents
echo "<h2>CSS File Contents (first 500 characters):</h2>";

$cssFiles = ['assets/css/style.css', 'assets/css/dark-mode.css'];

foreach ($cssFiles as $cssFile) {
    echo "<h3>{$cssFile}:</h3>";
    if (file_exists($cssFile) && is_readable($cssFile)) {
        $content = file_get_contents($cssFile);
        echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 200px; overflow: auto;'>";
        echo htmlspecialchars(substr($content, 0, 500));
        if (strlen($content) > 500) {
            echo "\n... (truncated)";
        }
        echo "</pre>";
    } else {
        echo "<p style='color: red;'>File not accessible!</p>";
    }
}

// Test URL generation
echo "<h2>URL Generation Test:</h2>";
if (file_exists('includes/functions.php')) {
    try {
        require_once 'includes/functions.php';

        echo "<p><strong>getBaseUrl():</strong> " . getBaseUrl() . "</p>";
        echo "<p><strong>Style CSS URL:</strong> " . getBaseUrl() . "/assets/css/style.css</p>";
        echo "<p><strong>Dark Mode CSS URL:</strong> " . getBaseUrl() . "/assets/css/dark-mode.css</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error loading functions.php: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>Cannot load functions.php</p>";
}

// Test HTTP access to CSS files
echo "<h2>HTTP Access Test:</h2>";
$baseUrl = isset($_SERVER['HTTP_HOST']) ? 
    (isset($_SERVER['HTTPS']) ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']) : 
    'http://localhost';

$cssUrls = [
    $baseUrl . '/assets/css/style.css',
    $baseUrl . '/assets/css/dark-mode.css'
];

foreach ($cssUrls as $url) {
    echo "<p><strong>Testing:</strong> <a href='{$url}' target='_blank'>{$url}</a></p>";
    
    // Try to get headers
    $headers = @get_headers($url);
    if ($headers) {
        echo "<p>Status: {$headers[0]}</p>";
    } else {
        echo "<p style='color: red;'>Could not fetch headers</p>";
    }
    echo "<hr>";
}

// Check .htaccess rules
echo "<h2>.htaccess Content:</h2>";
if (file_exists('.htaccess')) {
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    echo htmlspecialchars(file_get_contents('.htaccess'));
    echo "</pre>";
} else {
    echo "<p style='color: red;'>.htaccess file not found!</p>";
}
?>
