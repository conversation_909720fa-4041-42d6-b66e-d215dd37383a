<?php
/**
 * Minimal Hospitals List View
 * This file displays a minimal version of the hospitals list for testing
 */

// Set page title
$pageTitle = 'Hospitals';
$pageSubtitle = 'Manage hospital facilities and their information';

// Start output buffering
ob_start();
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?php echo getBaseUrl(); ?>/dashboard"><i class="fas fa-home"></i></a></li>
        <li class="breadcrumb-item active" aria-current="page">Hospitals</li>
    </ol>
</nav>

<!-- Header Section -->
<div class="card border-0 mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-hospital fa-2x text-primary"></i>
                    </div>
                    <div>
                        <h1 class="h2 mb-1">Hospitals Management</h1>
                        <p class="text-muted mb-0">Manage hospital facilities and their information</p>
                        <small class="text-info">
                            <i class="fas fa-info-circle me-1"></i>
                            <?php echo count($hospitals); ?> hospitals registered
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <a href="<?php echo getBaseUrl(); ?>/hospitals/create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add Hospital
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3><?php echo count($hospitals); ?></h3>
                <p class="mb-0">Total Hospitals</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3><?php echo array_sum(array_column($hospitals, 'department_count')); ?></h3>
                <p class="mb-0">Total Departments</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h3><?php echo array_sum(array_column($hospitals, 'device_count')); ?></h3>
                <p class="mb-0">Total Devices</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h3><?php echo count(array_unique(array_column($hospitals, 'country'))); ?></h3>
                <p class="mb-0">Countries</p>
            </div>
        </div>
    </div>
</div>

<!-- Hospitals Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>Hospitals List
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($hospitals)): ?>
            <div class="text-center py-5">
                <i class="fas fa-hospital fa-3x text-muted mb-3"></i>
                <h5>No Hospitals Found</h5>
                <p class="text-muted">No hospitals are available in the system.</p>
                <a href="<?php echo getBaseUrl(); ?>/hospitals/create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add First Hospital
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Hospital</th>
                            <th>Location</th>
                            <th>Contact</th>
                            <th>Departments</th>
                            <th>Devices</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($hospitals as $hospital): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-hospital text-primary me-2"></i>
                                        <div>
                                            <strong><?php echo htmlspecialchars($hospital['name']); ?></strong>
                                            <?php if (!empty($hospital['address'])): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars(substr($hospital['address'], 0, 50)); ?>...</small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <?php if (!empty($hospital['city'])): ?>
                                            <i class="fas fa-city text-info me-1"></i>
                                            <?php echo htmlspecialchars($hospital['city']); ?>
                                        <?php endif; ?>
                                        <?php if (!empty($hospital['country'])): ?>
                                            <br>
                                            <i class="fas fa-flag text-success me-1"></i>
                                            <span class="badge bg-light text-dark"><?php echo htmlspecialchars($hospital['country']); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <i class="fas fa-phone text-primary me-1"></i>
                                        <a href="tel:<?php echo htmlspecialchars($hospital['phone']); ?>" class="text-decoration-none">
                                            <?php echo htmlspecialchars($hospital['phone']); ?>
                                        </a>
                                        <?php if (!empty($hospital['email'])): ?>
                                            <br>
                                            <i class="fas fa-envelope text-info me-1"></i>
                                            <a href="mailto:<?php echo htmlspecialchars($hospital['email']); ?>" class="text-decoration-none small">
                                                <?php echo htmlspecialchars($hospital['email']); ?>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info fs-6">
                                        <i class="fas fa-building me-1"></i>
                                        <?php echo (int)$hospital['department_count']; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-success fs-6">
                                        <i class="fas fa-microscope me-1"></i>
                                        <?php echo (int)$hospital['device_count']; ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo getBaseUrl(); ?>/hospitals/view/<?php echo $hospital['id']; ?>"
                                           class="btn btn-sm btn-outline-primary"
                                           title="View Hospital">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo getBaseUrl(); ?>/hospitals/edit/<?php echo $hospital['id']; ?>"
                                           class="btn btn-sm btn-outline-secondary"
                                           title="Edit Hospital">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<div class="mt-4">
    <p class="text-muted">
        <i class="fas fa-info-circle me-1"></i>
        This is a minimal version of the hospitals page for testing purposes.
        <a href="simple_hospitals_test.php">View standalone test</a> |
        <a href="debug_hospitals.php">Debug information</a>
    </p>
</div>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
