<?php
/**
 * Test Models
 * This page tests the model functionality
 */

// Include the configuration and functions
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('login');
}

// Get models
require_once 'models/Hospital.php';
require_once 'models/Department.php';
require_once 'models/Device.php';

echo "<h1>Model Testing</h1>";

try {
    // Test database connection
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Initialize models
    $hospitalModel = new Hospital($pdo);
    $departmentModel = new Department($pdo);
    $deviceModel = new Device($pdo);
    
    echo "<p style='color: green;'>✓ Models initialized successfully</p>";
    
    // Test Hospital model
    echo "<h2>Testing Hospital Model</h2>";
    
    $hospitals = $hospitalModel->getAll();
    echo "<p>Hospital count: " . count($hospitals) . "</p>";
    
    if (count($hospitals) > 0) {
        $firstHospital = $hospitals[0];
        echo "<p>First hospital: " . htmlspecialchars($firstHospital['name']) . "</p>";
        
        // Test department count
        $deptCount = $departmentModel->countByHospital($firstHospital['id']);
        echo "<p>Departments in first hospital: $deptCount</p>";
        
        // Test device count
        $deviceCount = $deviceModel->countByHospital($firstHospital['id']);
        echo "<p>Devices in first hospital: $deviceCount</p>";
        
        // Test enhanced hospital data
        $enhancedHospital = $firstHospital;
        $enhancedHospital['department_count'] = $deptCount;
        $enhancedHospital['device_count'] = $deviceCount;
        
        if (!isset($enhancedHospital['city'])) $enhancedHospital['city'] = '';
        if (!isset($enhancedHospital['country'])) $enhancedHospital['country'] = '';
        if (!isset($enhancedHospital['email'])) $enhancedHospital['email'] = '';
        
        echo "<h3>Enhanced Hospital Data:</h3>";
        echo "<pre>";
        print_r($enhancedHospital);
        echo "</pre>";
    } else {
        echo "<p style='color: orange;'>No hospitals found. <a href='create_sample_hospitals.php'>Create sample data</a></p>";
    }
    
    // Test Department model
    echo "<h2>Testing Department Model</h2>";
    
    $departments = $departmentModel->getAll();
    echo "<p>Total departments: " . count($departments) . "</p>";
    
    // Test Device model
    echo "<h2>Testing Device Model</h2>";
    
    $devices = $deviceModel->getAll();
    echo "<p>Total devices: " . count($devices) . "</p>";
    
    echo "<h2>Controller Simulation</h2>";
    
    // Simulate what the controller does
    $hospitals = $hospitalModel->getAll();
    
    if (!is_array($hospitals)) {
        $hospitals = [];
    }
    
    foreach ($hospitals as &$hospital) {
        $hospital['department_count'] = $departmentModel->countByHospital($hospital['id']);
        $hospital['device_count'] = $deviceModel->countByHospital($hospital['id']);
        
        if (!isset($hospital['city'])) $hospital['city'] = '';
        if (!isset($hospital['country'])) $hospital['country'] = '';
        if (!isset($hospital['email'])) $hospital['email'] = '';
    }
    
    echo "<p style='color: green;'>✓ Controller simulation successful</p>";
    echo "<p>Processed hospitals: " . count($hospitals) . "</p>";
    
    if (count($hospitals) > 0) {
        echo "<h3>Sample Enhanced Hospital:</h3>";
        echo "<pre>";
        print_r($hospitals[0]);
        echo "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
    echo "<pre>";
    print_r($e->getTrace());
    echo "</pre>";
}

echo "<h2>Quick Links</h2>";
echo "<ul>";
echo "<li><a href='" . getBaseUrl() . "/hospitals'>Go to Hospitals Page</a></li>";
echo "<li><a href='" . getBaseUrl() . "/debug_hospitals.php'>Debug Hospitals</a></li>";
echo "<li><a href='" . getBaseUrl() . "/create_sample_hospitals.php'>Create Sample Data</a></li>";
echo "<li><a href='" . getBaseUrl() . "/dashboard'>Dashboard</a></li>";
echo "</ul>";
?>
