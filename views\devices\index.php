<?php
/**
 * Devices List View
 * 
 * This file displays the list of devices.
 */

// Set page title
$pageTitle = __('devices');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo __('devices_management'); ?></h1>
        <p class="text-muted mb-0"><?php echo __('manage_all_medical_devices'); ?></p>
    </div>

    <div class="d-flex gap-2">
        <?php if (hasPermission('manage_devices')): ?>
        <a href="<?php echo getBaseUrl(); ?>/devices/create" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i><?php echo __('add_device'); ?>
        </a>
        <?php endif; ?>

        <div class="dropdown">
            <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-download me-2"></i><?php echo __('export'); ?>
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/devices/export?format=pdf">
                    <i class="fas fa-file-pdf me-2 text-danger"></i>PDF
                </a></li>
                <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/devices/export?format=excel">
                    <i class="fas fa-file-excel me-2 text-success"></i>Excel
                </a></li>
                <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/devices/export?format=csv">
                    <i class="fas fa-file-csv me-2 text-info"></i>CSV
                </a></li>
            </ul>
        </div>
    </div>
</div>

<!-- Enhanced Filters -->
<div class="card card-glass mb-4 fade-in-up">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-filter me-2"></i><?php echo __('filters'); ?>
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="<?php echo getBaseUrl(); ?>/devices" class="row g-3" id="filterForm">
            <div class="col-md-3">
                <label for="hospital_id" class="form-label"><?php echo __('hospital'); ?></label>
                <select class="form-select" id="hospital_id" name="hospital_id">
                    <option value=""><?php echo __('all_hospitals'); ?></option>
                    <?php foreach ($hospitals as $hospital): ?>
                        <option value="<?php echo $hospital['id']; ?>" <?php echo ($hospitalId == $hospital['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($hospital['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="department_id" class="form-label"><?php echo __('department'); ?></label>
                <select class="form-select" id="department_id" name="department_id">
                    <option value=""><?php echo __('all_departments'); ?></option>
                    <?php foreach ($departments as $department): ?>
                        <option value="<?php echo $department['id']; ?>" <?php echo ($departmentId == $department['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($department['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="status" class="form-label"><?php echo __('status'); ?></label>
                <select class="form-select" id="status" name="status">
                    <option value=""><?php echo __('all_statuses'); ?></option>
                    <option value="operational" <?php echo ($status == 'operational') ? 'selected' : ''; ?>><?php echo __('operational'); ?></option>
                    <option value="under_maintenance" <?php echo ($status == 'under_maintenance') ? 'selected' : ''; ?>><?php echo __('under_maintenance'); ?></option>
                    <option value="out_of_order" <?php echo ($status == 'out_of_order') ? 'selected' : ''; ?>><?php echo __('out_of_order'); ?></option>
                    <option value="retired" <?php echo ($status == 'retired') ? 'selected' : ''; ?>><?php echo __('retired'); ?></option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="search" class="form-label"><?php echo __('search'); ?></label>
                <div class="input-group">
                    <input type="text" class="form-control" id="search" name="search" value="<?php echo htmlspecialchars($search ?? ''); ?>" placeholder="<?php echo __('search_devices'); ?>">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>

            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                        <i class="fas fa-times me-1"></i><?php echo __('clear_filters'); ?>
                    </button>
                    <small class="text-muted">
                        <?php echo count($devices); ?> <?php echo __('devices_found'); ?>
                    </small>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Enhanced Devices Table -->
<div class="card fade-in-up" style="animation-delay: 0.2s;">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i><?php echo __('devices_list'); ?>
            </h5>
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-outline-primary" onclick="toggleView('table')" id="tableViewBtn">
                    <i class="fas fa-table"></i>
                </button>
                <button class="btn btn-sm btn-outline-primary" onclick="toggleView('grid')" id="gridViewBtn">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive" id="tableView">
            <table class="table table-hover mb-0" id="devices-table">
                <thead>
                    <tr>
                        <th><?php echo __('name'); ?></th>
                        <th><?php echo __('model'); ?></th>
                        <th><?php echo __('serial_number'); ?></th>
                        <th><?php echo __('hospital'); ?></th>
                        <th><?php echo __('department'); ?></th>
                        <th><?php echo __('status'); ?></th>
                        <th><?php echo __('warranty'); ?></th>
                        <th><?php echo __('actions'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($devices)): ?>
                        <tr>
                            <td colspan="8" class="text-center"><?php echo __('no_devices'); ?></td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($devices as $device): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($device['name']); ?></td>
                                <td><?php echo htmlspecialchars($device['model']); ?></td>
                                <td><?php echo htmlspecialchars($device['serial_number']); ?></td>
                                <td>
                                    <?php if (hasPermission('view_hospitals')): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/hospitals/view/<?php echo $device['hospital_id']; ?>">
                                            <?php echo htmlspecialchars($device['hospital_name']); ?>
                                        </a>
                                    <?php else: ?>
                                        <?php echo htmlspecialchars($device['hospital_name']); ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (hasPermission('view_departments')): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/departments/view/<?php echo $device['department_id']; ?>">
                                            <?php echo htmlspecialchars($device['department_name']); ?>
                                        </a>
                                    <?php else: ?>
                                        <?php echo htmlspecialchars($device['department_name']); ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo getStatusColor($device['status']); ?>">
                                        <?php echo __($device['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php 
                                    $warrantyExpiry = new DateTime($device['warranty_expiry']);
                                    $now = new DateTime();
                                    $isExpired = $warrantyExpiry < $now;
                                    ?>
                                    <span class="<?php echo $isExpired ? 'text-danger' : 'text-success'; ?>">
                                        <?php echo $warrantyExpiry->format('Y-m-d'); ?>
                                        <?php if ($isExpired): ?>
                                            <i class="fas fa-exclamation-triangle ms-1"></i>
                                        <?php endif; ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="<?php echo getBaseUrl(); ?>/devices/view/<?php echo $device['id']; ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        
                                        <?php if (hasPermission('manage_devices')): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/devices/edit/<?php echo $device['id']; ?>" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        
                                        <a href="<?php echo getBaseUrl(); ?>/devices/qrcode/<?php echo $device['id']; ?>" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-qrcode"></i>
                                        </a>
                                        
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="confirmDelete(<?php echo $device['id']; ?>, '<?php echo htmlspecialchars($device['name']); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo __('confirm_delete'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p><?php echo __('delete_device_confirm'); ?></p>
                <p><strong id="deviceName"></strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel'); ?></button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <button type="submit" class="btn btn-danger"><?php echo __('delete'); ?></button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(deviceId, deviceName) {
    document.getElementById('deviceName').textContent = deviceName;
    document.getElementById('deleteForm').action = '<?php echo getBaseUrl(); ?>/devices/delete/' + deviceId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Auto-submit form when filters change
document.getElementById('hospital_id').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('department_id').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('status').addEventListener('change', function() {
    this.form.submit();
});
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
