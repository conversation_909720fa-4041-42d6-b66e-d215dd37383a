<?php
/**
 * Test Styling Page
 * 
 * This file is for testing the enhanced Bootstrap styling.
 */

// Start session
session_start();

// Set default timezone
date_default_timezone_set('UTC');

// Load configuration
require_once 'config/database.php';

// Load includes
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('login.php');
}

// Get the current user
$currentUser = getCurrentUser();

// Get the current language
$currentLanguage = getCurrentLanguage();

// Set page title
$pageTitle = 'Styling Test';
$pageSubtitle = 'Testing enhanced Bootstrap components';

// Start output buffering
ob_start();
?>

<!-- Test Cards -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card card-primary fade-in-up">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-test-tube me-2"></i>Enhanced Card
                </h5>
            </div>
            <div class="card-body">
                <p>This is an enhanced card with modern styling, gradients, and animations.</p>
                <div class="d-flex gap-2">
                    <button class="btn btn-primary btn-sm">Primary</button>
                    <button class="btn btn-success btn-sm">Success</button>
                    <button class="btn btn-warning btn-sm">Warning</button>
                    <button class="btn btn-danger btn-sm">Danger</button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card card-glass fade-in-up" style="animation-delay: 0.2s;">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-magic me-2"></i>Glass Card
                </h5>
            </div>
            <div class="card-body">
                <p>This is a glass card with backdrop blur effect and transparency.</p>
                <div class="progress mb-3">
                    <div class="progress-bar" style="width: 75%"></div>
                </div>
                <small class="text-muted">75% Complete</small>
            </div>
        </div>
    </div>
</div>

<!-- Test Stats Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card primary h-100 fade-in-up">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number animate" data-target="150">0</div>
                        <div class="stats-text">Total Items</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-cube"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card success h-100 fade-in-up" style="animation-delay: 0.1s;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number animate" data-target="89">0</div>
                        <div class="stats-text">Active</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card warning h-100 fade-in-up" style="animation-delay: 0.2s;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number animate" data-target="12">0</div>
                        <div class="stats-text">Pending</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card danger h-100 fade-in-up" style="animation-delay: 0.3s;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number animate" data-target="3">0</div>
                        <div class="stats-text">Issues</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Forms -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card fade-in-up">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>Enhanced Form
                </h5>
            </div>
            <div class="card-body">
                <form>
                    <div class="mb-3">
                        <label for="testInput" class="form-label">Test Input</label>
                        <input type="text" class="form-control" id="testInput" placeholder="Enter some text">
                    </div>
                    
                    <div class="mb-3">
                        <label for="testSelect" class="form-label">Test Select</label>
                        <select class="form-select" id="testSelect">
                            <option>Choose an option</option>
                            <option>Option 1</option>
                            <option>Option 2</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="testTextarea" class="form-label">Test Textarea</label>
                        <textarea class="form-control" id="testTextarea" rows="3"></textarea>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">Submit</button>
                        <button type="reset" class="btn btn-outline-secondary">Reset</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card fade-in-up" style="animation-delay: 0.2s;">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>Enhanced Table
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Item 1</td>
                                <td><span class="badge bg-success">Active</span></td>
                                <td>
                                    <div class="btn-group">
                                        <button class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>Item 2</td>
                                <td><span class="badge bg-warning">Pending</span></td>
                                <td>
                                    <div class="btn-group">
                                        <button class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Alerts -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-primary slide-in-right">
            <i class="fas fa-info-circle me-2"></i>
            This is an enhanced primary alert with icons and animations.
        </div>
        
        <div class="alert alert-success slide-in-right" style="animation-delay: 0.1s;">
            <i class="fas fa-check-circle me-2"></i>
            This is an enhanced success alert with gradients.
        </div>
        
        <div class="alert alert-warning slide-in-right" style="animation-delay: 0.2s;">
            <i class="fas fa-exclamation-triangle me-2"></i>
            This is an enhanced warning alert with modern styling.
        </div>
        
        <div class="alert alert-danger slide-in-right" style="animation-delay: 0.3s;">
            <i class="fas fa-times-circle me-2"></i>
            This is an enhanced danger alert with shadows.
        </div>
    </div>
</div>

<script>
// Animate counters
function animateCounters() {
    const counters = document.querySelectorAll(".stats-number.animate");
    
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute("data-target"));
        const increment = target / 100;
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current);
        }, 20);
    });
}

// Initialize animations when page loads
document.addEventListener("DOMContentLoaded", function() {
    setTimeout(animateCounters, 500);
});
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
