<?php
/**
 * Hospitals Diagnostic Page
 * This page provides comprehensive diagnostics for the hospitals system
 */

// Start session
session_start();

// Include the configuration and functions
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hospitals System Diagnostic - Medical Device Management</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- FontAwesome CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
        }
        
        .diagnostic-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        .status-info { color: #17a2b8; }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="diagnostic-card">
            <div class="card-body text-center">
                <h1 class="mb-4">
                    <i class="fas fa-stethoscope text-primary me-2"></i>
                    Hospitals System Diagnostic
                </h1>
                <p class="lead text-muted">Comprehensive system health check for the Medical Device Management System</p>
            </div>
        </div>

        <?php
        $diagnostics = [];
        $overallStatus = 'good';

        // 1. Check if user is logged in
        $diagnostics['Authentication'] = [];
        if (isLoggedIn()) {
            $currentUser = getCurrentUser();
            $diagnostics['Authentication']['status'] = 'good';
            $diagnostics['Authentication']['message'] = 'User is logged in';
            $diagnostics['Authentication']['details'] = [
                'User ID' => $currentUser['id'],
                'Full Name' => $currentUser['full_name'],
                'Role' => $currentUser['role'],
                'Hospital ID' => $currentUser['hospital_id'] ?: 'None'
            ];
        } else {
            $diagnostics['Authentication']['status'] = 'warning';
            $diagnostics['Authentication']['message'] = 'User is not logged in';
            $diagnostics['Authentication']['details'] = ['Redirect to login required'];
        }

        // 2. Check database connection
        $diagnostics['Database'] = [];
        try {
            $testPdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
            $diagnostics['Database']['status'] = 'good';
            $diagnostics['Database']['message'] = 'Database connection successful';
            $diagnostics['Database']['details'] = [
                'Host' => DB_HOST,
                'Database' => DB_NAME,
                'User' => DB_USER
            ];
        } catch (Exception $e) {
            $diagnostics['Database']['status'] = 'error';
            $diagnostics['Database']['message'] = 'Database connection failed';
            $diagnostics['Database']['details'] = ['Error' => $e->getMessage()];
            $overallStatus = 'error';
        }

        // 3. Check required files
        $diagnostics['Files'] = [];
        $requiredFiles = [
            'config/database.php',
            'includes/functions.php',
            'includes/auth.php',
            'models/Hospital.php',
            'models/Department.php',
            'models/Device.php',
            'controllers/hospitals.php',
            'views/hospitals/index.php',
            'views/layout.php'
        ];
        
        $missingFiles = [];
        foreach ($requiredFiles as $file) {
            if (!file_exists($file)) {
                $missingFiles[] = $file;
            }
        }
        
        if (empty($missingFiles)) {
            $diagnostics['Files']['status'] = 'good';
            $diagnostics['Files']['message'] = 'All required files exist';
            $diagnostics['Files']['details'] = ['Files checked' => count($requiredFiles)];
        } else {
            $diagnostics['Files']['status'] = 'error';
            $diagnostics['Files']['message'] = 'Missing required files';
            $diagnostics['Files']['details'] = ['Missing files' => $missingFiles];
            $overallStatus = 'error';
        }

        // 4. Check models
        $diagnostics['Models'] = [];
        if (isLoggedIn() && isset($testPdo)) {
            try {
                require_once 'models/Hospital.php';
                require_once 'models/Department.php';
                require_once 'models/Device.php';
                
                $hospitalModel = new Hospital($testPdo);
                $departmentModel = new Department($testPdo);
                $deviceModel = new Device($testPdo);
                
                $hospitals = $hospitalModel->getAll();
                
                $diagnostics['Models']['status'] = 'good';
                $diagnostics['Models']['message'] = 'Models initialized successfully';
                $diagnostics['Models']['details'] = [
                    'Hospitals found' => count($hospitals),
                    'Hospital model' => 'Working',
                    'Department model' => 'Working',
                    'Device model' => 'Working'
                ];
            } catch (Exception $e) {
                $diagnostics['Models']['status'] = 'error';
                $diagnostics['Models']['message'] = 'Model initialization failed';
                $diagnostics['Models']['details'] = ['Error' => $e->getMessage()];
                $overallStatus = 'error';
            }
        } else {
            $diagnostics['Models']['status'] = 'warning';
            $diagnostics['Models']['message'] = 'Cannot test models (login or database issue)';
            $diagnostics['Models']['details'] = [];
        }

        // 5. Check permissions
        $diagnostics['Permissions'] = [];
        if (isLoggedIn()) {
            $permissions = [
                'view_hospitals' => hasPermission('view_hospitals'),
                'manage_hospitals' => hasPermission('manage_hospitals'),
                'view_departments' => hasPermission('view_departments'),
                'manage_departments' => hasPermission('manage_departments'),
                'view_devices' => hasPermission('view_devices'),
                'manage_devices' => hasPermission('manage_devices')
            ];
            
            $diagnostics['Permissions']['status'] = 'info';
            $diagnostics['Permissions']['message'] = 'User permissions checked';
            $diagnostics['Permissions']['details'] = $permissions;
        } else {
            $diagnostics['Permissions']['status'] = 'warning';
            $diagnostics['Permissions']['message'] = 'Cannot check permissions (not logged in)';
            $diagnostics['Permissions']['details'] = [];
        }

        // 6. Check database tables
        $diagnostics['Database Tables'] = [];
        if (isset($testPdo)) {
            try {
                $stmt = $testPdo->query("SHOW TABLES");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                $requiredTables = ['hospitals', 'departments', 'devices', 'users'];
                $missingTables = array_diff($requiredTables, $tables);
                
                if (empty($missingTables)) {
                    $diagnostics['Database Tables']['status'] = 'good';
                    $diagnostics['Database Tables']['message'] = 'All required tables exist';
                    $diagnostics['Database Tables']['details'] = [
                        'Total tables' => count($tables),
                        'Required tables' => implode(', ', $requiredTables)
                    ];
                } else {
                    $diagnostics['Database Tables']['status'] = 'error';
                    $diagnostics['Database Tables']['message'] = 'Missing required tables';
                    $diagnostics['Database Tables']['details'] = [
                        'Missing tables' => $missingTables,
                        'Existing tables' => $tables
                    ];
                    $overallStatus = 'error';
                }
            } catch (Exception $e) {
                $diagnostics['Database Tables']['status'] = 'error';
                $diagnostics['Database Tables']['message'] = 'Cannot check tables';
                $diagnostics['Database Tables']['details'] = ['Error' => $e->getMessage()];
                $overallStatus = 'error';
            }
        }

        // Display diagnostics
        foreach ($diagnostics as $category => $info) {
            $statusClass = 'status-' . ($info['status'] === 'good' ? 'good' : ($info['status'] === 'warning' ? 'warning' : ($info['status'] === 'error' ? 'error' : 'info')));
            $iconClass = $info['status'] === 'good' ? 'fa-check-circle' : ($info['status'] === 'warning' ? 'fa-exclamation-triangle' : ($info['status'] === 'error' ? 'fa-times-circle' : 'fa-info-circle'));
            ?>
            <div class="diagnostic-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas <?php echo $iconClass; ?> <?php echo $statusClass; ?> me-2"></i>
                        <?php echo $category; ?>
                        <span class="badge bg-<?php echo $info['status'] === 'good' ? 'success' : ($info['status'] === 'warning' ? 'warning' : ($info['status'] === 'error' ? 'danger' : 'info')); ?> ms-2">
                            <?php echo strtoupper($info['status']); ?>
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <p class="<?php echo $statusClass; ?>"><?php echo $info['message']; ?></p>
                    <?php if (!empty($info['details'])): ?>
                        <div class="code-block">
                            <?php foreach ($info['details'] as $key => $value): ?>
                                <div><strong><?php echo htmlspecialchars($key); ?>:</strong> 
                                <?php 
                                if (is_array($value)) {
                                    echo implode(', ', array_map('htmlspecialchars', $value));
                                } else {
                                    echo htmlspecialchars($value);
                                }
                                ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php
        }
        ?>

        <!-- Overall Status -->
        <div class="diagnostic-card">
            <div class="card-header bg-<?php echo $overallStatus === 'good' ? 'success' : ($overallStatus === 'warning' ? 'warning' : 'danger'); ?> text-white">
                <h5 class="mb-0">
                    <i class="fas fa-<?php echo $overallStatus === 'good' ? 'thumbs-up' : ($overallStatus === 'warning' ? 'exclamation-triangle' : 'times-circle'); ?> me-2"></i>
                    Overall System Status: <?php echo strtoupper($overallStatus); ?>
                </h5>
            </div>
            <div class="card-body">
                <?php if ($overallStatus === 'good'): ?>
                    <p class="text-success">✅ All systems are functioning correctly. You can proceed to use the hospitals management system.</p>
                <?php elseif ($overallStatus === 'warning'): ?>
                    <p class="text-warning">⚠️ Some issues detected but the system should still function. Check the warnings above.</p>
                <?php else: ?>
                    <p class="text-danger">❌ Critical issues detected. Please resolve the errors above before using the system.</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="diagnostic-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>System Pages:</h6>
                        <div class="d-grid gap-2">
                            <a href="<?php echo getBaseUrl(); ?>/hospitals" class="btn btn-primary">
                                <i class="fas fa-hospital me-2"></i>Go to Hospitals Page
                            </a>
                            <a href="<?php echo getBaseUrl(); ?>/dashboard" class="btn btn-outline-primary">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Test Pages:</h6>
                        <div class="d-grid gap-2">
                            <a href="simple_hospitals_test.php" class="btn btn-info">
                                <i class="fas fa-vial me-2"></i>Simple Test
                            </a>
                            <a href="create_sample_hospitals.php" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Create Sample Data
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
